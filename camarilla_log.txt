20. 5. 2025 9:18:23: Processing bar: 1, Time: 15. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:23: Calculating Camarilla levels for date: 2025-05-15, CurrentBar: 1
20. 5. 2025 9:18:23: Debugging all available bars for date calculation:
20. 5. 2025 9:18:23: Bar 0: Time=15. 5. 2025 2:00:00, Date=15. 5. 2025 0:00:00, Open=5901,25, High=5901,75, Low=5892,25, Close=5896,75
20. 5. 2025 9:18:23: Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using default H4/L4 values for 2025-05-15 - could not find previous day's data
20. 5. 2025 9:18:23: IMPORTANT: Also caching default H4/L4 values for previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 2, Time: 15. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 3, Time: 15. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 4, Time: 15. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 5, Time: 15. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 6, Time: 15. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 7, Time: 15. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 8, Time: 15. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 9, Time: 15. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 10, Time: 15. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 11, Time: 15. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 12, Time: 15. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 13, Time: 15. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 14, Time: 15. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 15, Time: 15. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 16, Time: 15. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 17, Time: 15. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 18, Time: 15. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 19, Time: 15. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 20, Time: 15. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 21, Time: 15. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 22, Time: 15. 5. 2025 23:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:23: Processing bar: 23, Time: 16. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 9:18:23: Calculating Camarilla levels for date: 2025-05-16, CurrentBar: 23
20. 5. 2025 9:18:23: Debugging all available bars for date calculation:
20. 5. 2025 9:18:23: Bar 0: Time=16. 5. 2025 1:00:00, Date=16. 5. 2025 0:00:00, Open=5935,5, High=5936, Low=5930,75, Close=5931
20. 5. 2025 9:18:23: Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Found 22 bars for previous day. Last bar index: 1, Close: 5934
20. 5. 2025 9:18:23: FINAL DATA FOR PREVIOUS DAY: High=5944,5, Low=5867, Close=5934
20. 5. 2025 9:18:23: Using standard Camarilla formula without corrections
20. 5. 2025 9:18:23: Calculation: Range=77,5, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 9:18:23: H4 = 5934 + (77,5 * 0,55) = 5976,62
20. 5. 2025 9:18:23: L4 = 5934 - (77,5 * 0,55) = 5891,38
20. 5. 2025 9:18:23: FINAL H4/L4 VALUES: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Calculated H4/L4 for 2025-05-16 based on previous day: H4=5976,62, L4=5891,38, PrevHigh=5944,5, PrevLow=5867, PrevClose=5934
20. 5. 2025 9:18:23: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Using H4/L4 values for current day 2025-05-16: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Drawing H4/L4 lines for 2025-05-16: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 24, Time: 16. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 25, Time: 16. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 26, Time: 16. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 27, Time: 16. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 28, Time: 16. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 29, Time: 16. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 30, Time: 16. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 31, Time: 16. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 32, Time: 16. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 33, Time: 16. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 34, Time: 16. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 35, Time: 16. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 36, Time: 16. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 37, Time: 16. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 38, Time: 16. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 39, Time: 16. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 40, Time: 16. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 41, Time: 16. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 42, Time: 16. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 43, Time: 16. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 44, Time: 16. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 45, Time: 16. 5. 2025 23:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 9:18:23: Processing bar: 46, Time: 19. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 9:18:23: Calculating Camarilla levels for date: 2025-05-19, CurrentBar: 46
20. 5. 2025 9:18:23: Debugging all available bars for date calculation:
20. 5. 2025 9:18:23: Bar 0: Time=19. 5. 2025 1:00:00, Date=19. 5. 2025 0:00:00, Open=5930,5, High=5940,5, Low=5917,75, Close=5939
20. 5. 2025 9:18:23: Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Found 23 bars for previous day. Last bar index: 1, Close: 5948,5
20. 5. 2025 9:18:23: FINAL DATA FOR PREVIOUS DAY: High=5977,75, Low=5923, Close=5948,5
20. 5. 2025 9:18:23: Using standard Camarilla formula without corrections
20. 5. 2025 9:18:23: Calculation: Range=54,75, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 9:18:23: H4 = 5948,5 + (54,75 * 0,55) = 5978,61
20. 5. 2025 9:18:23: L4 = 5948,5 - (54,75 * 0,55) = 5918,39
20. 5. 2025 9:18:23: FINAL H4/L4 VALUES: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Calculated H4/L4 for 2025-05-19 based on previous day: H4=5978,61, L4=5918,39, PrevHigh=5977,75, PrevLow=5923, PrevClose=5948,5
20. 5. 2025 9:18:23: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Using H4/L4 values for current day 2025-05-19: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Drawing H4/L4 lines for 2025-05-19: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 47, Time: 19. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 48, Time: 19. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 49, Time: 19. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 50, Time: 19. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 51, Time: 19. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 52, Time: 19. 5. 2025 7:00:00, Position: Short
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 53, Time: 19. 5. 2025 8:00:00, Position: Short
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 54, Time: 19. 5. 2025 9:00:00, Position: Short
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 55, Time: 19. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 56, Time: 19. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 57, Time: 19. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 58, Time: 19. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 59, Time: 19. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 60, Time: 19. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 61, Time: 19. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 62, Time: 19. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 63, Time: 19. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 64, Time: 19. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 65, Time: 19. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 66, Time: 19. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 67, Time: 19. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 68, Time: 19. 5. 2025 23:00:00, Position: Long
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 9:18:23: Processing bar: 69, Time: 20. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 9:18:23: Calculating Camarilla levels for date: 2025-05-20, CurrentBar: 69
20. 5. 2025 9:18:23: Debugging all available bars for date calculation:
20. 5. 2025 9:18:23: Bar 0: Time=20. 5. 2025 1:00:00, Date=20. 5. 2025 0:00:00, Open=5980,25, High=5981,5, Low=5975,5, Close=5979,75
20. 5. 2025 9:18:23: Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:23: Found 23 bars for previous day. Last bar index: 1, Close: 5980,75
20. 5. 2025 9:18:23: FINAL DATA FOR PREVIOUS DAY: High=5987,5, Low=5892,75, Close=5980,75
20. 5. 2025 9:18:23: Using standard Camarilla formula without corrections
20. 5. 2025 9:18:23: Calculation: Range=94,75, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 9:18:23: H4 = 5980,75 + (94,75 * 0,55) = 6032,86
20. 5. 2025 9:18:23: L4 = 5980,75 - (94,75 * 0,55) = 5928,64
20. 5. 2025 9:18:23: FINAL H4/L4 VALUES: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:23: Calculated H4/L4 for 2025-05-20 based on previous day: H4=6032,86, L4=5928,64, PrevHigh=5987,5, PrevLow=5892,75, PrevClose=5980,75
20. 5. 2025 9:18:23: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:23: Using H4/L4 values for current day 2025-05-20: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:23: Drawing H4/L4 lines for 2025-05-20: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:23: Processing bar: 70, Time: 20. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:23: Processing bar: 71, Time: 20. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:23: Processing bar: 72, Time: 20. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:23: Processing bar: 73, Time: 20. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:23: Processing bar: 74, Time: 20. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:23: Processing bar: 75, Time: 20. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:23: Processing bar: 76, Time: 20. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:23: Processing bar: 77, Time: 20. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:23: Processing bar: 78, Time: 20. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:23: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:23: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 9:18:31: Processing bar: 1, Time: 15. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:31: Calculating Camarilla levels for date: 2025-05-15, CurrentBar: 1
20. 5. 2025 9:18:31: Debugging all available bars for date calculation:
20. 5. 2025 9:18:31: Bar 0: Time=15. 5. 2025 2:00:00, Date=15. 5. 2025 0:00:00, Open=1,1204, High=1,121, Low=1,1201, Close=1,1203
20. 5. 2025 9:18:31: Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using default H4/L4 values for 2025-05-15 - could not find previous day's data
20. 5. 2025 9:18:31: IMPORTANT: Also caching default H4/L4 values for previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 2, Time: 15. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 3, Time: 15. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 4, Time: 15. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 5, Time: 15. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 6, Time: 15. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 7, Time: 15. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 8, Time: 15. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 9, Time: 15. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 10, Time: 15. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 11, Time: 15. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 12, Time: 15. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 13, Time: 15. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 14, Time: 15. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 15, Time: 15. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 16, Time: 15. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 17, Time: 15. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 18, Time: 15. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 19, Time: 15. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 20, Time: 15. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 21, Time: 15. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 22, Time: 15. 5. 2025 23:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:31: Processing bar: 23, Time: 16. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 9:18:31: Calculating Camarilla levels for date: 2025-05-16, CurrentBar: 23
20. 5. 2025 9:18:31: Debugging all available bars for date calculation:
20. 5. 2025 9:18:31: Bar 0: Time=16. 5. 2025 1:00:00, Date=16. 5. 2025 0:00:00, Open=1,1208, High=1,1214, Low=1,1206, Close=1,1212
20. 5. 2025 9:18:31: Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Found 22 bars for previous day. Last bar index: 1, Close: 1,1205
20. 5. 2025 9:18:31: FINAL DATA FOR PREVIOUS DAY: High=1,1249, Low=1,1191, Close=1,1205
20. 5. 2025 9:18:31: Using standard Camarilla formula without corrections
20. 5. 2025 9:18:31: Calculation: Range=0,00580000000000003, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 9:18:31: H4 = 1,1205 + (0,00580000000000003 * 0,55) = 1,12
20. 5. 2025 9:18:31: L4 = 1,1205 - (0,00580000000000003 * 0,55) = 1,12
20. 5. 2025 9:18:31: FINAL H4/L4 VALUES: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Calculated H4/L4 for 2025-05-16 based on previous day: H4=1,12, L4=1,12, PrevHigh=1,1249, PrevLow=1,1191, PrevClose=1,1205
20. 5. 2025 9:18:31: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Using H4/L4 values for current day 2025-05-16: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Drawing H4/L4 lines for 2025-05-16: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 24, Time: 16. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 25, Time: 16. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 26, Time: 16. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 27, Time: 16. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 28, Time: 16. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 29, Time: 16. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 30, Time: 16. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 31, Time: 16. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 32, Time: 16. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 33, Time: 16. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 34, Time: 16. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 35, Time: 16. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 36, Time: 16. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 37, Time: 16. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 38, Time: 16. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 39, Time: 16. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 40, Time: 16. 5. 2025 18:00:00, Position: Short
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 41, Time: 16. 5. 2025 19:00:00, Position: Short
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 42, Time: 16. 5. 2025 20:00:00, Position: Short
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 43, Time: 16. 5. 2025 21:00:00, Position: Short
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 44, Time: 16. 5. 2025 22:00:00, Position: Short
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 45, Time: 16. 5. 2025 23:00:00, Position: Short
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-15: H4=1,12, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 46, Time: 19. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 9:18:31: Calculating Camarilla levels for date: 2025-05-19, CurrentBar: 46
20. 5. 2025 9:18:31: Debugging all available bars for date calculation:
20. 5. 2025 9:18:31: Bar 0: Time=19. 5. 2025 1:00:00, Date=19. 5. 2025 0:00:00, Open=1,1192, High=1,1214, Low=1,119, Close=1,121
20. 5. 2025 9:18:31: Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Found 23 bars for previous day. Last bar index: 1, Close: 1,1182
20. 5. 2025 9:18:31: FINAL DATA FOR PREVIOUS DAY: High=1,124, Low=1,1152, Close=1,1182
20. 5. 2025 9:18:31: Using standard Camarilla formula without corrections
20. 5. 2025 9:18:31: Calculation: Range=0,00880000000000014, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 9:18:31: H4 = 1,1182 + (0,00880000000000014 * 0,55) = 1,12
20. 5. 2025 9:18:31: L4 = 1,1182 - (0,00880000000000014 * 0,55) = 1,11
20. 5. 2025 9:18:31: FINAL H4/L4 VALUES: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Calculated H4/L4 for 2025-05-19 based on previous day: H4=1,12, L4=1,11, PrevHigh=1,124, PrevLow=1,1152, PrevClose=1,1182
20. 5. 2025 9:18:31: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Using H4/L4 values for current day 2025-05-19: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Drawing H4/L4 lines for 2025-05-19: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 47, Time: 19. 5. 2025 2:00:00, Position: Long
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 48, Time: 19. 5. 2025 3:00:00, Position: Long
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 49, Time: 19. 5. 2025 4:00:00, Position: Long
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 50, Time: 19. 5. 2025 5:00:00, Position: Long
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 51, Time: 19. 5. 2025 6:00:00, Position: Long
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 52, Time: 19. 5. 2025 7:00:00, Position: Long
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 53, Time: 19. 5. 2025 8:00:00, Position: Long
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 54, Time: 19. 5. 2025 9:00:00, Position: Long
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 55, Time: 19. 5. 2025 10:00:00, Position: Long
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 56, Time: 19. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 57, Time: 19. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 58, Time: 19. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 59, Time: 19. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 60, Time: 19. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 61, Time: 19. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 62, Time: 19. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 63, Time: 19. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 64, Time: 19. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 65, Time: 19. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 66, Time: 19. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 67, Time: 19. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 68, Time: 19. 5. 2025 23:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-16: H4=1,12, L4=1,11
20. 5. 2025 9:18:31: Processing bar: 69, Time: 20. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 9:18:31: Calculating Camarilla levels for date: 2025-05-20, CurrentBar: 69
20. 5. 2025 9:18:31: Debugging all available bars for date calculation:
20. 5. 2025 9:18:31: Bar 0: Time=20. 5. 2025 1:00:00, Date=20. 5. 2025 0:00:00, Open=1,1262, High=1,1264, Low=1,1254, Close=1,1254
20. 5. 2025 9:18:31: Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:31: Found 23 bars for previous day. Last bar index: 1, Close: 1,1264
20. 5. 2025 9:18:31: FINAL DATA FOR PREVIOUS DAY: High=1,1307, Low=1,119, Close=1,1264
20. 5. 2025 9:18:31: Using standard Camarilla formula without corrections
20. 5. 2025 9:18:31: Calculation: Range=0,0117, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 9:18:31: H4 = 1,1264 + (0,0117 * 0,55) = 1,13
20. 5. 2025 9:18:31: L4 = 1,1264 - (0,0117 * 0,55) = 1,12
20. 5. 2025 9:18:31: FINAL H4/L4 VALUES: H4=1,13, L4=1,12
20. 5. 2025 9:18:31: Calculated H4/L4 for 2025-05-20 based on previous day: H4=1,13, L4=1,12, PrevHigh=1,1307, PrevLow=1,119, PrevClose=1,1264
20. 5. 2025 9:18:31: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 9:18:31: Using H4/L4 values for current day 2025-05-20: H4=1,13, L4=1,12
20. 5. 2025 9:18:31: Drawing H4/L4 lines for 2025-05-20: H4=1,13, L4=1,12
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 70, Time: 20. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 71, Time: 20. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 72, Time: 20. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 73, Time: 20. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 74, Time: 20. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 75, Time: 20. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 76, Time: 20. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 77, Time: 20. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 9:18:31: Processing bar: 78, Time: 20. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:31: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:31: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 9:18:41: Processing bar: 1, Time: 15. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:41: Calculating Camarilla levels for date: 2025-05-15, CurrentBar: 1
20. 5. 2025 9:18:41: Debugging all available bars for date calculation:
20. 5. 2025 9:18:41: Bar 0: Time=15. 5. 2025 2:00:00, Date=15. 5. 2025 0:00:00, Open=0,643, High=0,6431, Low=0,6424, Close=0,643
20. 5. 2025 9:18:41: Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using default H4/L4 values for 2025-05-15 - could not find previous day's data
20. 5. 2025 9:18:41: IMPORTANT: Also caching default H4/L4 values for previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 2, Time: 15. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 3, Time: 15. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 4, Time: 15. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 5, Time: 15. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 6, Time: 15. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 7, Time: 15. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 8, Time: 15. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 9, Time: 15. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 10, Time: 15. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 11, Time: 15. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 12, Time: 15. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 13, Time: 15. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 14, Time: 15. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 15, Time: 15. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 16, Time: 15. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 17, Time: 15. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 18, Time: 15. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 19, Time: 15. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 20, Time: 15. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 21, Time: 15. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 22, Time: 15. 5. 2025 23:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:41: Processing bar: 23, Time: 16. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 9:18:41: Calculating Camarilla levels for date: 2025-05-16, CurrentBar: 23
20. 5. 2025 9:18:41: Debugging all available bars for date calculation:
20. 5. 2025 9:18:41: Bar 0: Time=16. 5. 2025 1:00:00, Date=16. 5. 2025 0:00:00, Open=0,6407, High=0,6411, Low=0,6405, Close=0,6408
20. 5. 2025 9:18:41: Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Found 22 bars for previous day. Last bar index: 1, Close: 0,6409
20. 5. 2025 9:18:41: FINAL DATA FOR PREVIOUS DAY: High=0,6462, Low=0,6393, Close=0,6409
20. 5. 2025 9:18:41: Using standard Camarilla formula without corrections
20. 5. 2025 9:18:41: Calculation: Range=0,00690000000000002, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 9:18:41: H4 = 0,6409 + (0,00690000000000002 * 0,55) = 0,64
20. 5. 2025 9:18:41: L4 = 0,6409 - (0,00690000000000002 * 0,55) = 0,64
20. 5. 2025 9:18:41: FINAL H4/L4 VALUES: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Calculated H4/L4 for 2025-05-16 based on previous day: H4=0,64, L4=0,64, PrevHigh=0,6462, PrevLow=0,6393, PrevClose=0,6409
20. 5. 2025 9:18:41: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Using H4/L4 values for current day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Drawing H4/L4 lines for 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 24, Time: 16. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 25, Time: 16. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 26, Time: 16. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 27, Time: 16. 5. 2025 5:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 28, Time: 16. 5. 2025 6:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 29, Time: 16. 5. 2025 7:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 30, Time: 16. 5. 2025 8:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 31, Time: 16. 5. 2025 9:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 32, Time: 16. 5. 2025 10:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 33, Time: 16. 5. 2025 11:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 34, Time: 16. 5. 2025 12:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 35, Time: 16. 5. 2025 13:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 36, Time: 16. 5. 2025 14:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 37, Time: 16. 5. 2025 15:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 38, Time: 16. 5. 2025 16:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 39, Time: 16. 5. 2025 17:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 40, Time: 16. 5. 2025 18:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 41, Time: 16. 5. 2025 19:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 42, Time: 16. 5. 2025 20:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 43, Time: 16. 5. 2025 21:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 44, Time: 16. 5. 2025 22:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 45, Time: 16. 5. 2025 23:00:00, Position: Long
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-15: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 46, Time: 19. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 9:18:41: Calculating Camarilla levels for date: 2025-05-19, CurrentBar: 46
20. 5. 2025 9:18:41: Debugging all available bars for date calculation:
20. 5. 2025 9:18:41: Bar 0: Time=19. 5. 2025 1:00:00, Date=19. 5. 2025 0:00:00, Open=0,6405, High=0,6414, Low=0,6403, Close=0,641
20. 5. 2025 9:18:41: Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Found 23 bars for previous day. Last bar index: 1, Close: 0,641
20. 5. 2025 9:18:41: FINAL DATA FOR PREVIOUS DAY: High=0,6438, Low=0,6391, Close=0,641
20. 5. 2025 9:18:41: Using standard Camarilla formula without corrections
20. 5. 2025 9:18:41: Calculation: Range=0,00470000000000004, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 9:18:41: H4 = 0,641 + (0,00470000000000004 * 0,55) = 0,64
20. 5. 2025 9:18:41: L4 = 0,641 - (0,00470000000000004 * 0,55) = 0,64
20. 5. 2025 9:18:41: FINAL H4/L4 VALUES: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Calculated H4/L4 for 2025-05-19 based on previous day: H4=0,64, L4=0,64, PrevHigh=0,6438, PrevLow=0,6391, PrevClose=0,641
20. 5. 2025 9:18:41: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Using H4/L4 values for current day 2025-05-19: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Drawing H4/L4 lines for 2025-05-19: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 47, Time: 19. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 48, Time: 19. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 49, Time: 19. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 50, Time: 19. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 51, Time: 19. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 52, Time: 19. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 53, Time: 19. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 54, Time: 19. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 55, Time: 19. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 56, Time: 19. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 57, Time: 19. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 58, Time: 19. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 59, Time: 19. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 60, Time: 19. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 61, Time: 19. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 62, Time: 19. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 63, Time: 19. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 64, Time: 19. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 65, Time: 19. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 66, Time: 19. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 67, Time: 19. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 68, Time: 19. 5. 2025 23:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-16: H4=0,64, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 69, Time: 20. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 9:18:41: Calculating Camarilla levels for date: 2025-05-20, CurrentBar: 69
20. 5. 2025 9:18:41: Debugging all available bars for date calculation:
20. 5. 2025 9:18:41: Bar 0: Time=20. 5. 2025 1:00:00, Date=20. 5. 2025 0:00:00, Open=0,646, High=0,6462, Low=0,6456, Close=0,6457
20. 5. 2025 9:18:41: Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:41: Found 23 bars for previous day. Last bar index: 1, Close: 0,646
20. 5. 2025 9:18:41: FINAL DATA FOR PREVIOUS DAY: High=0,6467, Low=0,6401, Close=0,646
20. 5. 2025 9:18:41: Using standard Camarilla formula without corrections
20. 5. 2025 9:18:41: Calculation: Range=0,00660000000000005, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 9:18:41: H4 = 0,646 + (0,00660000000000005 * 0,55) = 0,65
20. 5. 2025 9:18:41: L4 = 0,646 - (0,00660000000000005 * 0,55) = 0,64
20. 5. 2025 9:18:41: FINAL H4/L4 VALUES: H4=0,65, L4=0,64
20. 5. 2025 9:18:41: Calculated H4/L4 for 2025-05-20 based on previous day: H4=0,65, L4=0,64, PrevHigh=0,6467, PrevLow=0,6401, PrevClose=0,646
20. 5. 2025 9:18:41: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 9:18:41: Using H4/L4 values for current day 2025-05-20: H4=0,65, L4=0,64
20. 5. 2025 9:18:41: Drawing H4/L4 lines for 2025-05-20: H4=0,65, L4=0,64
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 70, Time: 20. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 71, Time: 20. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 72, Time: 20. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 73, Time: 20. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 74, Time: 20. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 75, Time: 20. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 76, Time: 20. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 77, Time: 20. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 9:18:41: Processing bar: 78, Time: 20. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:41: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:41: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 9:18:48: Processing bar: 1, Time: 15. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:48: Calculating Camarilla levels for date: 2025-05-15, CurrentBar: 1
20. 5. 2025 9:18:48: Debugging all available bars for date calculation:
20. 5. 2025 9:18:48: Bar 0: Time=15. 5. 2025 2:00:00, Date=15. 5. 2025 0:00:00, Open=21379,75, High=21382,25, Low=21357, Close=21375,5
20. 5. 2025 9:18:48: Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using default H4/L4 values for 2025-05-15 - could not find previous day's data
20. 5. 2025 9:18:48: IMPORTANT: Also caching default H4/L4 values for previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 2, Time: 15. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 3, Time: 15. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 4, Time: 15. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 5, Time: 15. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 6, Time: 15. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 7, Time: 15. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 8, Time: 15. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 9, Time: 15. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 10, Time: 15. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 11, Time: 15. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 12, Time: 15. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 13, Time: 15. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 14, Time: 15. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 15, Time: 15. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 16, Time: 15. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 17, Time: 15. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 18, Time: 15. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 19, Time: 15. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 20, Time: 15. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 21, Time: 15. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 22, Time: 15. 5. 2025 23:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 9:18:48: Processing bar: 23, Time: 16. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 9:18:48: Calculating Camarilla levels for date: 2025-05-16, CurrentBar: 23
20. 5. 2025 9:18:48: Debugging all available bars for date calculation:
20. 5. 2025 9:18:48: Bar 0: Time=16. 5. 2025 1:00:00, Date=16. 5. 2025 0:00:00, Open=21397,25, High=21403,5, Low=21379,25, Close=21383,5
20. 5. 2025 9:18:48: Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Found 22 bars for previous day. Last bar index: 1, Close: 21396,25
20. 5. 2025 9:18:48: FINAL DATA FOR PREVIOUS DAY: High=21529,75, Low=21192,25, Close=21396,25
20. 5. 2025 9:18:48: Using standard Camarilla formula without corrections
20. 5. 2025 9:18:48: Calculation: Range=337,5, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 9:18:48: H4 = 21396,25 + (337,5 * 0,55) = 21581,88
20. 5. 2025 9:18:48: L4 = 21396,25 - (337,5 * 0,55) = 21210,62
20. 5. 2025 9:18:48: FINAL H4/L4 VALUES: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Calculated H4/L4 for 2025-05-16 based on previous day: H4=21581,88, L4=21210,62, PrevHigh=21529,75, PrevLow=21192,25, PrevClose=21396,25
20. 5. 2025 9:18:48: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Using H4/L4 values for current day 2025-05-16: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Drawing H4/L4 lines for 2025-05-16: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 24, Time: 16. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 25, Time: 16. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 26, Time: 16. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 27, Time: 16. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 28, Time: 16. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 29, Time: 16. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 30, Time: 16. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 31, Time: 16. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 32, Time: 16. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 33, Time: 16. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 34, Time: 16. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 35, Time: 16. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 36, Time: 16. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 37, Time: 16. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 38, Time: 16. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 39, Time: 16. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 40, Time: 16. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 41, Time: 16. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 42, Time: 16. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 43, Time: 16. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 44, Time: 16. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 45, Time: 16. 5. 2025 23:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-15: H4=21581,88, L4=21210,62
20. 5. 2025 9:18:48: Processing bar: 46, Time: 19. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 9:18:48: Calculating Camarilla levels for date: 2025-05-19, CurrentBar: 46
20. 5. 2025 9:18:48: Debugging all available bars for date calculation:
20. 5. 2025 9:18:48: Bar 0: Time=19. 5. 2025 1:00:00, Date=19. 5. 2025 0:00:00, Open=21313,5, High=21374, Low=21286,5, Close=21369,5
20. 5. 2025 9:18:48: Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Found 23 bars for previous day. Last bar index: 1, Close: 21404,25
20. 5. 2025 9:18:48: FINAL DATA FOR PREVIOUS DAY: High=21513, Low=21320, Close=21404,25
20. 5. 2025 9:18:48: Using standard Camarilla formula without corrections
20. 5. 2025 9:18:48: Calculation: Range=193, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 9:18:48: H4 = 21404,25 + (193 * 0,55) = 21510,4
20. 5. 2025 9:18:48: L4 = 21404,25 - (193 * 0,55) = 21298,1
20. 5. 2025 9:18:48: FINAL H4/L4 VALUES: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Calculated H4/L4 for 2025-05-19 based on previous day: H4=21510,4, L4=21298,1, PrevHigh=21513, PrevLow=21320, PrevClose=21404,25
20. 5. 2025 9:18:48: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Using H4/L4 values for current day 2025-05-19: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Drawing H4/L4 lines for 2025-05-19: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 47, Time: 19. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 48, Time: 19. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 49, Time: 19. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 50, Time: 19. 5. 2025 5:00:00, Position: Short
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 51, Time: 19. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 52, Time: 19. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 53, Time: 19. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 54, Time: 19. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 55, Time: 19. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 56, Time: 19. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 57, Time: 19. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 58, Time: 19. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 59, Time: 19. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 60, Time: 19. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 61, Time: 19. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 62, Time: 19. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 63, Time: 19. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 64, Time: 19. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 65, Time: 19. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 66, Time: 19. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 67, Time: 19. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 68, Time: 19. 5. 2025 23:00:00, Position: Long
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-16: H4=21510,4, L4=21298,1
20. 5. 2025 9:18:48: Processing bar: 69, Time: 20. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 9:18:48: Calculating Camarilla levels for date: 2025-05-20, CurrentBar: 69
20. 5. 2025 9:18:48: Debugging all available bars for date calculation:
20. 5. 2025 9:18:48: Bar 0: Time=20. 5. 2025 1:00:00, Date=20. 5. 2025 0:00:00, Open=21516,75, High=21516,75, Low=21492,75, Close=21499,5
20. 5. 2025 9:18:48: Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:48: Found 23 bars for previous day. Last bar index: 1, Close: 21517,5
20. 5. 2025 9:18:48: FINAL DATA FOR PREVIOUS DAY: High=21545,5, Low=21112,25, Close=21517,5
20. 5. 2025 9:18:48: Using standard Camarilla formula without corrections
20. 5. 2025 9:18:48: Calculation: Range=433,25, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 9:18:48: H4 = 21517,5 + (433,25 * 0,55) = 21755,79
20. 5. 2025 9:18:48: L4 = 21517,5 - (433,25 * 0,55) = 21279,21
20. 5. 2025 9:18:48: FINAL H4/L4 VALUES: H4=21755,79, L4=21279,21
20. 5. 2025 9:18:48: Calculated H4/L4 for 2025-05-20 based on previous day: H4=21755,79, L4=21279,21, PrevHigh=21545,5, PrevLow=21112,25, PrevClose=21517,5
20. 5. 2025 9:18:48: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 9:18:48: Using H4/L4 values for current day 2025-05-20: H4=21755,79, L4=21279,21
20. 5. 2025 9:18:48: Drawing H4/L4 lines for 2025-05-20: H4=21755,79, L4=21279,21
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 9:18:48: Processing bar: 70, Time: 20. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 9:18:48: Processing bar: 71, Time: 20. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 9:18:48: Processing bar: 72, Time: 20. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 9:18:48: Processing bar: 73, Time: 20. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 9:18:48: Processing bar: 74, Time: 20. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 9:18:48: Processing bar: 75, Time: 20. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 9:18:48: Processing bar: 76, Time: 20. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 9:18:48: Processing bar: 77, Time: 20. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 9:18:48: Processing bar: 78, Time: 20. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 9:18:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 9:18:48: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 10:00:01: Processing bar: 79, Time: 20. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 10:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 10:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 10:00:01: Processing bar: 79, Time: 20. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 10:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 10:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 10:00:01: Processing bar: 79, Time: 20. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 10:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 10:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 10:00:13: Processing bar: 79, Time: 20. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 10:00:13: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 10:00:13: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 11:00:01: Processing bar: 80, Time: 20. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 11:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 11:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 11:00:01: Processing bar: 80, Time: 20. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 11:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 11:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 11:00:02: Processing bar: 80, Time: 20. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 11:00:02: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 11:00:02: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 11:00:03: Processing bar: 80, Time: 20. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 11:00:03: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 11:00:03: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 12:00:01: Processing bar: 81, Time: 20. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 12:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 12:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 12:00:01: Processing bar: 81, Time: 20. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 12:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 12:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 12:00:07: Processing bar: 81, Time: 20. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 12:00:07: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 12:00:07: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 12:02:10: Processing bar: 81, Time: 20. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 12:02:10: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 12:02:10: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 13:00:01: Processing bar: 82, Time: 20. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 13:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 13:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 13:00:01: Processing bar: 82, Time: 20. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 13:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 13:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 13:00:04: Processing bar: 82, Time: 20. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 13:00:04: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 13:00:04: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 13:01:50: Processing bar: 82, Time: 20. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 13:01:50: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 13:01:50: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 14:00:01: Processing bar: 83, Time: 20. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 14:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 14:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 14:00:01: Processing bar: 83, Time: 20. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 14:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 14:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 14:00:01: Processing bar: 83, Time: 20. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 14:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 14:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 14:00:01: Processing bar: 83, Time: 20. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 14:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 14:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 15:00:01: Processing bar: 84, Time: 20. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 15:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 15:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 15:00:01: Processing bar: 84, Time: 20. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 15:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 15:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 15:00:01: Processing bar: 84, Time: 20. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 15:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 15:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 15:00:03: Processing bar: 84, Time: 20. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 15:00:03: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 15:00:03: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 16:00:01: Processing bar: 85, Time: 20. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 16:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 16:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 16:00:01: Processing bar: 85, Time: 20. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 16:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 16:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 16:00:01: Processing bar: 85, Time: 20. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 16:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 16:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 16:00:04: Processing bar: 85, Time: 20. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 16:00:04: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 16:00:04: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 17:00:01: Processing bar: 86, Time: 20. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 17:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 17:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 17:00:01: Processing bar: 86, Time: 20. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 17:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 17:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 17:00:04: Processing bar: 86, Time: 20. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 17:00:04: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 17:00:04: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 17:00:33: Processing bar: 86, Time: 20. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 17:00:33: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 17:00:33: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 18:00:01: Processing bar: 87, Time: 20. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 18:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 18:00:01: Processing bar: 87, Time: 20. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 18:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 18:00:01: Processing bar: 87, Time: 20. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 18:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 18:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 18:00:21: Processing bar: 87, Time: 20. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 18:00:21: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 18:00:21: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 19:00:01: Processing bar: 88, Time: 20. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=21755,79, L4=21279,21
20. 5. 2025 19:00:01: Processing bar: 88, Time: 20. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:00:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:00:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:00:09: Processing bar: 88, Time: 20. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:00:09: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:00:09: Using entry H4/L4 values from previous day 2025-05-19: H4=1,13, L4=1,12
20. 5. 2025 19:00:16: Processing bar: 88, Time: 20. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:00:16: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:00:16: Using entry H4/L4 values from previous day 2025-05-19: H4=0,65, L4=0,64
20. 5. 2025 19:30:47: Processing bar: 1, Time: 15. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:30:47: Calculating Camarilla levels for date: 2025-05-15, CurrentBar: 1
20. 5. 2025 19:30:47: Debugging all available bars for date calculation:
20. 5. 2025 19:30:47: Bar 0: Time=15. 5. 2025 17:00:00, Date=15. 5. 2025 0:00:00, Open=5895,25, High=5905,5, Low=5886,75, Close=5900,75
20. 5. 2025 19:30:47: Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:30:47: Using default H4/L4 values for 2025-05-15 - could not find previous day's data
20. 5. 2025 19:30:47: IMPORTANT: Also caching default H4/L4 values for previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:30:47: Processing bar: 2, Time: 15. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:30:47: Processing bar: 3, Time: 15. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:30:47: Processing bar: 4, Time: 15. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:30:47: Processing bar: 5, Time: 15. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:30:47: Processing bar: 6, Time: 15. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:30:47: Processing bar: 7, Time: 15. 5. 2025 22:15:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:30:47: Processing bar: 8, Time: 16. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:30:47: Calculating Camarilla levels for date: 2025-05-16, CurrentBar: 8
20. 5. 2025 19:30:47: Debugging all available bars for date calculation:
20. 5. 2025 19:30:47: Bar 0: Time=16. 5. 2025 16:00:00, Date=16. 5. 2025 0:00:00, Open=5945,25, High=5951,25, Low=5931,75, Close=5945
20. 5. 2025 19:30:47: Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:30:47: Found 7 bars for previous day. Last bar index: 1, Close: 5937,25
20. 5. 2025 19:30:47: FINAL DATA FOR PREVIOUS DAY: High=5944,5, Low=5886,75, Close=5937,25
20. 5. 2025 19:30:47: Using standard Camarilla formula without corrections
20. 5. 2025 19:30:47: Calculation: Range=57,75, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:30:47: H4 = 5937,25 + (57,75 * 0,55) = 5969,01
20. 5. 2025 19:30:47: L4 = 5937,25 - (57,75 * 0,55) = 5905,49
20. 5. 2025 19:30:47: FINAL H4/L4 VALUES: H4=5969,01, L4=5905,49
20. 5. 2025 19:30:47: Calculated H4/L4 for 2025-05-16 based on previous day: H4=5969,01, L4=5905,49, PrevHigh=5944,5, PrevLow=5886,75, PrevClose=5937,25
20. 5. 2025 19:30:47: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-15: H4=5969,01, L4=5905,49
20. 5. 2025 19:30:47: Using H4/L4 values for current day 2025-05-16: H4=5969,01, L4=5905,49
20. 5. 2025 19:30:47: Drawing H4/L4 lines for 2025-05-16: H4=5969,01, L4=5905,49
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-15: H4=5969,01, L4=5905,49
20. 5. 2025 19:30:47: Processing bar: 9, Time: 16. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-15: H4=5969,01, L4=5905,49
20. 5. 2025 19:30:47: Processing bar: 10, Time: 16. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-15: H4=5969,01, L4=5905,49
20. 5. 2025 19:30:47: Processing bar: 11, Time: 16. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-15: H4=5969,01, L4=5905,49
20. 5. 2025 19:30:47: Processing bar: 12, Time: 16. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-15: H4=5969,01, L4=5905,49
20. 5. 2025 19:30:47: Processing bar: 13, Time: 16. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-15: H4=5969,01, L4=5905,49
20. 5. 2025 19:30:47: Processing bar: 14, Time: 16. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-15: H4=5969,01, L4=5905,49
20. 5. 2025 19:30:47: Processing bar: 15, Time: 16. 5. 2025 22:15:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-15: H4=5969,01, L4=5905,49
20. 5. 2025 19:30:47: Processing bar: 16, Time: 19. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:30:47: Calculating Camarilla levels for date: 2025-05-19, CurrentBar: 16
20. 5. 2025 19:30:47: Debugging all available bars for date calculation:
20. 5. 2025 19:30:47: Bar 0: Time=19. 5. 2025 16:00:00, Date=19. 5. 2025 0:00:00, Open=5917,75, High=5945,5, Low=5902,5, Close=5943,75
20. 5. 2025 19:30:47: Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:30:47: Found 8 bars for previous day. Last bar index: 1, Close: 5972,75
20. 5. 2025 19:30:47: FINAL DATA FOR PREVIOUS DAY: High=5977,75, Low=5924,75, Close=5972,75
20. 5. 2025 19:30:47: Using standard Camarilla formula without corrections
20. 5. 2025 19:30:47: Calculation: Range=53, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:30:47: H4 = 5972,75 + (53 * 0,55) = 6001,9
20. 5. 2025 19:30:47: L4 = 5972,75 - (53 * 0,55) = 5943,6
20. 5. 2025 19:30:47: FINAL H4/L4 VALUES: H4=6001,9, L4=5943,6
20. 5. 2025 19:30:47: Calculated H4/L4 for 2025-05-19 based on previous day: H4=6001,9, L4=5943,6, PrevHigh=5977,75, PrevLow=5924,75, PrevClose=5972,75
20. 5. 2025 19:30:47: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-16: H4=6001,9, L4=5943,6
20. 5. 2025 19:30:47: Using H4/L4 values for current day 2025-05-19: H4=6001,9, L4=5943,6
20. 5. 2025 19:30:47: Drawing H4/L4 lines for 2025-05-19: H4=6001,9, L4=5943,6
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-16: H4=6001,9, L4=5943,6
20. 5. 2025 19:30:47: Processing bar: 17, Time: 19. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-16: H4=6001,9, L4=5943,6
20. 5. 2025 19:30:47: Processing bar: 18, Time: 19. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-16: H4=6001,9, L4=5943,6
20. 5. 2025 19:30:47: Processing bar: 19, Time: 19. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-16: H4=6001,9, L4=5943,6
20. 5. 2025 19:30:47: Processing bar: 20, Time: 19. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-16: H4=6001,9, L4=5943,6
20. 5. 2025 19:30:47: Processing bar: 21, Time: 19. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-16: H4=6001,9, L4=5943,6
20. 5. 2025 19:30:47: Processing bar: 22, Time: 19. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-16: H4=6001,9, L4=5943,6
20. 5. 2025 19:30:47: Processing bar: 23, Time: 19. 5. 2025 22:15:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-16: H4=6001,9, L4=5943,6
20. 5. 2025 19:30:47: Processing bar: 24, Time: 20. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:30:47: Calculating Camarilla levels for date: 2025-05-20, CurrentBar: 24
20. 5. 2025 19:30:47: Debugging all available bars for date calculation:
20. 5. 2025 19:30:47: Bar 0: Time=20. 5. 2025 16:00:00, Date=20. 5. 2025 0:00:00, Open=5966,25, High=5971,25, Low=5950,5, Close=5959
20. 5. 2025 19:30:47: Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:30:47: Found 8 bars for previous day. Last bar index: 1, Close: 5973
20. 5. 2025 19:30:47: FINAL DATA FOR PREVIOUS DAY: High=5987,5, Low=5902,5, Close=5973
20. 5. 2025 19:30:47: Using standard Camarilla formula without corrections
20. 5. 2025 19:30:47: Calculation: Range=85, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:30:47: H4 = 5973 + (85 * 0,55) = 6019,75
20. 5. 2025 19:30:47: L4 = 5973 - (85 * 0,55) = 5926,25
20. 5. 2025 19:30:47: FINAL H4/L4 VALUES: H4=6019,75, L4=5926,25
20. 5. 2025 19:30:47: Calculated H4/L4 for 2025-05-20 based on previous day: H4=6019,75, L4=5926,25, PrevHigh=5987,5, PrevLow=5902,5, PrevClose=5973
20. 5. 2025 19:30:47: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-19: H4=6019,75, L4=5926,25
20. 5. 2025 19:30:47: Using H4/L4 values for current day 2025-05-20: H4=6019,75, L4=5926,25
20. 5. 2025 19:30:47: Drawing H4/L4 lines for 2025-05-20: H4=6019,75, L4=5926,25
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-19: H4=6019,75, L4=5926,25
20. 5. 2025 19:30:47: Processing bar: 25, Time: 20. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-19: H4=6019,75, L4=5926,25
20. 5. 2025 19:30:47: Processing bar: 26, Time: 20. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-19: H4=6019,75, L4=5926,25
20. 5. 2025 19:30:47: Processing bar: 27, Time: 20. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-19: H4=6019,75, L4=5926,25
20. 5. 2025 19:30:47: Processing bar: 28, Time: 20. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:30:47: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:30:47: Using entry H4/L4 values from previous day 2025-05-19: H4=6019,75, L4=5926,25
20. 5. 2025 19:31:59: Processing bar: 1, Time: 15. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 19:31:59: Calculating Camarilla levels for date: 2025-05-15, CurrentBar: 1
20. 5. 2025 19:31:59: Debugging all available bars for date calculation:
20. 5. 2025 19:31:59: Bar 0: Time=15. 5. 2025 2:00:00, Date=15. 5. 2025 0:00:00, Open=5901,25, High=5901,75, Low=5892,25, Close=5896,75
20. 5. 2025 19:31:59: Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using default H4/L4 values for 2025-05-15 - could not find previous day's data
20. 5. 2025 19:31:59: IMPORTANT: Also caching default H4/L4 values for previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 2, Time: 15. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 3, Time: 15. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 4, Time: 15. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 5, Time: 15. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 6, Time: 15. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 7, Time: 15. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 8, Time: 15. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 9, Time: 15. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 10, Time: 15. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 11, Time: 15. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 12, Time: 15. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 13, Time: 15. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 14, Time: 15. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 15, Time: 15. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 16, Time: 15. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 17, Time: 15. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 18, Time: 15. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 19, Time: 15. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 20, Time: 15. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 21, Time: 15. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 22, Time: 15. 5. 2025 23:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-14: H4=5900, L4=5800
20. 5. 2025 19:31:59: Processing bar: 23, Time: 16. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 19:31:59: Calculating Camarilla levels for date: 2025-05-16, CurrentBar: 23
20. 5. 2025 19:31:59: Debugging all available bars for date calculation:
20. 5. 2025 19:31:59: Bar 0: Time=16. 5. 2025 1:00:00, Date=16. 5. 2025 0:00:00, Open=5935,5, High=5936, Low=5930,75, Close=5931
20. 5. 2025 19:31:59: Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Found 22 bars for previous day. Last bar index: 1, Close: 5934
20. 5. 2025 19:31:59: FINAL DATA FOR PREVIOUS DAY: High=5944,5, Low=5867, Close=5934
20. 5. 2025 19:31:59: Using standard Camarilla formula without corrections
20. 5. 2025 19:31:59: Calculation: Range=77,5, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:31:59: H4 = 5934 + (77,5 * 0,55) = 5976,62
20. 5. 2025 19:31:59: L4 = 5934 - (77,5 * 0,55) = 5891,38
20. 5. 2025 19:31:59: FINAL H4/L4 VALUES: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Calculated H4/L4 for 2025-05-16 based on previous day: H4=5976,62, L4=5891,38, PrevHigh=5944,5, PrevLow=5867, PrevClose=5934
20. 5. 2025 19:31:59: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Using H4/L4 values for current day 2025-05-16: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Drawing H4/L4 lines for 2025-05-16: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 24, Time: 16. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 25, Time: 16. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 26, Time: 16. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 27, Time: 16. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 28, Time: 16. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 29, Time: 16. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 30, Time: 16. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 31, Time: 16. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 32, Time: 16. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 33, Time: 16. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 34, Time: 16. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 35, Time: 16. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 36, Time: 16. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 37, Time: 16. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 38, Time: 16. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 39, Time: 16. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 40, Time: 16. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 41, Time: 16. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 42, Time: 16. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 43, Time: 16. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 44, Time: 16. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 45, Time: 16. 5. 2025 23:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:31:59: Processing bar: 46, Time: 19. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 19:31:59: Calculating Camarilla levels for date: 2025-05-19, CurrentBar: 46
20. 5. 2025 19:31:59: Debugging all available bars for date calculation:
20. 5. 2025 19:31:59: Bar 0: Time=19. 5. 2025 1:00:00, Date=19. 5. 2025 0:00:00, Open=5930,5, High=5940,5, Low=5917,75, Close=5939
20. 5. 2025 19:31:59: Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Found 23 bars for previous day. Last bar index: 1, Close: 5948,5
20. 5. 2025 19:31:59: FINAL DATA FOR PREVIOUS DAY: High=5977,75, Low=5923, Close=5948,5
20. 5. 2025 19:31:59: Using standard Camarilla formula without corrections
20. 5. 2025 19:31:59: Calculation: Range=54,75, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:31:59: H4 = 5948,5 + (54,75 * 0,55) = 5978,61
20. 5. 2025 19:31:59: L4 = 5948,5 - (54,75 * 0,55) = 5918,39
20. 5. 2025 19:31:59: FINAL H4/L4 VALUES: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Calculated H4/L4 for 2025-05-19 based on previous day: H4=5978,61, L4=5918,39, PrevHigh=5977,75, PrevLow=5923, PrevClose=5948,5
20. 5. 2025 19:31:59: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Using H4/L4 values for current day 2025-05-19: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Drawing H4/L4 lines for 2025-05-19: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 47, Time: 19. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 48, Time: 19. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 49, Time: 19. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 50, Time: 19. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 51, Time: 19. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 52, Time: 19. 5. 2025 7:00:00, Position: Short
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 53, Time: 19. 5. 2025 8:00:00, Position: Short
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 54, Time: 19. 5. 2025 9:00:00, Position: Short
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 55, Time: 19. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 56, Time: 19. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 57, Time: 19. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 58, Time: 19. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 59, Time: 19. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 60, Time: 19. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 61, Time: 19. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 62, Time: 19. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 63, Time: 19. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 64, Time: 19. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 65, Time: 19. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 66, Time: 19. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 67, Time: 19. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 68, Time: 19. 5. 2025 23:00:00, Position: Long
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-16: H4=5978,61, L4=5918,39
20. 5. 2025 19:31:59: Processing bar: 69, Time: 20. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 19:31:59: Calculating Camarilla levels for date: 2025-05-20, CurrentBar: 69
20. 5. 2025 19:31:59: Debugging all available bars for date calculation:
20. 5. 2025 19:31:59: Bar 0: Time=20. 5. 2025 1:00:00, Date=20. 5. 2025 0:00:00, Open=5980,25, High=5981,5, Low=5975,5, Close=5979,75
20. 5. 2025 19:31:59: Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Found 23 bars for previous day. Last bar index: 1, Close: 5980,75
20. 5. 2025 19:31:59: FINAL DATA FOR PREVIOUS DAY: High=5987,5, Low=5892,75, Close=5980,75
20. 5. 2025 19:31:59: Using standard Camarilla formula without corrections
20. 5. 2025 19:31:59: Calculation: Range=94,75, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:31:59: H4 = 5980,75 + (94,75 * 0,55) = 6032,86
20. 5. 2025 19:31:59: L4 = 5980,75 - (94,75 * 0,55) = 5928,64
20. 5. 2025 19:31:59: FINAL H4/L4 VALUES: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Calculated H4/L4 for 2025-05-20 based on previous day: H4=6032,86, L4=5928,64, PrevHigh=5987,5, PrevLow=5892,75, PrevClose=5980,75
20. 5. 2025 19:31:59: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Using H4/L4 values for current day 2025-05-20: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Drawing H4/L4 lines for 2025-05-20: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 70, Time: 20. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 71, Time: 20. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 72, Time: 20. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 73, Time: 20. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 74, Time: 20. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 75, Time: 20. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 76, Time: 20. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 77, Time: 20. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 78, Time: 20. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 79, Time: 20. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 80, Time: 20. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 81, Time: 20. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 82, Time: 20. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 83, Time: 20. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 84, Time: 20. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 85, Time: 20. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 86, Time: 20. 5. 2025 18:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 87, Time: 20. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:31:59: Processing bar: 88, Time: 20. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:31:59: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:31:59: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 1, Time: 14. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:35:11: Calculating Camarilla levels for date: 2025-05-14, CurrentBar: 1
20. 5. 2025 19:35:11: Debugging all available bars for date calculation:
20. 5. 2025 19:35:11: Bar 0: Time=14. 5. 2025 20:00:00, Date=14. 5. 2025 0:00:00, Open=5910, High=5910,25, Low=5890, Close=5908,25
20. 5. 2025 19:35:11: Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:35:11: Using default H4/L4 values for 2025-05-14 - could not find previous day's data
20. 5. 2025 19:35:11: IMPORTANT: Also caching default H4/L4 values for previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:35:11: Processing bar: 2, Time: 14. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:35:11: Processing bar: 3, Time: 14. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:35:11: Processing bar: 4, Time: 14. 5. 2025 23:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:35:11: Processing bar: 5, Time: 15. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 19:35:11: Calculating Camarilla levels for date: 2025-05-15, CurrentBar: 5
20. 5. 2025 19:35:11: Debugging all available bars for date calculation:
20. 5. 2025 19:35:11: Bar 0: Time=15. 5. 2025 1:00:00, Date=15. 5. 2025 0:00:00, Open=5904,25, High=5904,25, Low=5900, Close=5901,5
20. 5. 2025 19:35:11: Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Found 4 bars for previous day. Last bar index: 1, Close: 5906
20. 5. 2025 19:35:11: FINAL DATA FOR PREVIOUS DAY: High=5917,5, Low=5890, Close=5906
20. 5. 2025 19:35:11: Using standard Camarilla formula without corrections
20. 5. 2025 19:35:11: Calculation: Range=27,5, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:35:11: H4 = 5906 + (27,5 * 0,55) = 5921,12
20. 5. 2025 19:35:11: L4 = 5906 - (27,5 * 0,55) = 5890,88
20. 5. 2025 19:35:11: FINAL H4/L4 VALUES: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Calculated H4/L4 for 2025-05-15 based on previous day: H4=5921,12, L4=5890,88, PrevHigh=5917,5, PrevLow=5890, PrevClose=5906
20. 5. 2025 19:35:11: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Using H4/L4 values for current day 2025-05-15: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Drawing H4/L4 lines for 2025-05-15: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 6, Time: 15. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 7, Time: 15. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 8, Time: 15. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 9, Time: 15. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 10, Time: 15. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 11, Time: 15. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 12, Time: 15. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 13, Time: 15. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 14, Time: 15. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 15, Time: 15. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 16, Time: 15. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 17, Time: 15. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 18, Time: 15. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 19, Time: 15. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 20, Time: 15. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 21, Time: 15. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 22, Time: 15. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 23, Time: 15. 5. 2025 20:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 24, Time: 15. 5. 2025 21:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 25, Time: 15. 5. 2025 22:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 26, Time: 15. 5. 2025 23:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:35:11: Processing bar: 27, Time: 16. 5. 2025 1:00:00, Position: Long
20. 5. 2025 19:35:11: Calculating Camarilla levels for date: 2025-05-16, CurrentBar: 27
20. 5. 2025 19:35:11: Debugging all available bars for date calculation:
20. 5. 2025 19:35:11: Bar 0: Time=16. 5. 2025 1:00:00, Date=16. 5. 2025 0:00:00, Open=5935,5, High=5936, Low=5930,75, Close=5931
20. 5. 2025 19:35:11: Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Found 22 bars for previous day. Last bar index: 1, Close: 5934
20. 5. 2025 19:35:11: FINAL DATA FOR PREVIOUS DAY: High=5944,5, Low=5867, Close=5934
20. 5. 2025 19:35:11: Using standard Camarilla formula without corrections
20. 5. 2025 19:35:11: Calculation: Range=77,5, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:35:11: H4 = 5934 + (77,5 * 0,55) = 5976,62
20. 5. 2025 19:35:11: L4 = 5934 - (77,5 * 0,55) = 5891,38
20. 5. 2025 19:35:11: FINAL H4/L4 VALUES: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Calculated H4/L4 for 2025-05-16 based on previous day: H4=5976,62, L4=5891,38, PrevHigh=5944,5, PrevLow=5867, PrevClose=5934
20. 5. 2025 19:35:11: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Using H4/L4 values for current day 2025-05-16: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Drawing H4/L4 lines for 2025-05-16: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 28, Time: 16. 5. 2025 2:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 29, Time: 16. 5. 2025 3:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 30, Time: 16. 5. 2025 4:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 31, Time: 16. 5. 2025 5:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 32, Time: 16. 5. 2025 6:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 33, Time: 16. 5. 2025 7:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 34, Time: 16. 5. 2025 8:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 35, Time: 16. 5. 2025 9:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 36, Time: 16. 5. 2025 10:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 37, Time: 16. 5. 2025 11:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 38, Time: 16. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 39, Time: 16. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 40, Time: 16. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 41, Time: 16. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 42, Time: 16. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 43, Time: 16. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:35:11: Processing bar: 44, Time: 19. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 19:35:11: Calculating Camarilla levels for date: 2025-05-19, CurrentBar: 44
20. 5. 2025 19:35:11: Debugging all available bars for date calculation:
20. 5. 2025 19:35:11: Bar 0: Time=19. 5. 2025 1:00:00, Date=19. 5. 2025 0:00:00, Open=5930,5, High=5940,5, Low=5917,75, Close=5939
20. 5. 2025 19:35:11: Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Found 17 bars for previous day. Last bar index: 1, Close: 5936,75
20. 5. 2025 19:35:11: FINAL DATA FOR PREVIOUS DAY: High=5958,25, Low=5923, Close=5936,75
20. 5. 2025 19:35:11: Using standard Camarilla formula without corrections
20. 5. 2025 19:35:11: Calculation: Range=35,25, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:35:11: H4 = 5936,75 + (35,25 * 0,55) = 5956,14
20. 5. 2025 19:35:11: L4 = 5936,75 - (35,25 * 0,55) = 5917,36
20. 5. 2025 19:35:11: FINAL H4/L4 VALUES: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Calculated H4/L4 for 2025-05-19 based on previous day: H4=5956,14, L4=5917,36, PrevHigh=5958,25, PrevLow=5923, PrevClose=5936,75
20. 5. 2025 19:35:11: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Using H4/L4 values for current day 2025-05-19: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Drawing H4/L4 lines for 2025-05-19: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 45, Time: 19. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 46, Time: 19. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 47, Time: 19. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 48, Time: 19. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 49, Time: 19. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 50, Time: 19. 5. 2025 7:00:00, Position: Short
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 51, Time: 19. 5. 2025 8:00:00, Position: Short
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 52, Time: 19. 5. 2025 9:00:00, Position: Short
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 53, Time: 19. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 54, Time: 19. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 55, Time: 19. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 56, Time: 19. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 57, Time: 19. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 58, Time: 19. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 59, Time: 19. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 60, Time: 19. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 61, Time: 19. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 62, Time: 19. 5. 2025 20:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 63, Time: 19. 5. 2025 21:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 64, Time: 19. 5. 2025 22:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 65, Time: 19. 5. 2025 23:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:35:11: Processing bar: 66, Time: 20. 5. 2025 1:00:00, Position: Long
20. 5. 2025 19:35:11: Calculating Camarilla levels for date: 2025-05-20, CurrentBar: 66
20. 5. 2025 19:35:11: Debugging all available bars for date calculation:
20. 5. 2025 19:35:11: Bar 0: Time=20. 5. 2025 1:00:00, Date=20. 5. 2025 0:00:00, Open=5965, High=5968, Low=5963, Close=5967,25
20. 5. 2025 19:35:11: Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Found 22 bars for previous day. Last bar index: 1, Close: 5980,75
20. 5. 2025 19:35:11: FINAL DATA FOR PREVIOUS DAY: High=5987,5, Low=5892,75, Close=5980,75
20. 5. 2025 19:35:11: Using standard Camarilla formula without corrections
20. 5. 2025 19:35:11: Calculation: Range=94,75, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:35:11: H4 = 5980,75 + (94,75 * 0,55) = 6032,86
20. 5. 2025 19:35:11: L4 = 5980,75 - (94,75 * 0,55) = 5928,64
20. 5. 2025 19:35:11: FINAL H4/L4 VALUES: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Calculated H4/L4 for 2025-05-20 based on previous day: H4=6032,86, L4=5928,64, PrevHigh=5987,5, PrevLow=5892,75, PrevClose=5980,75
20. 5. 2025 19:35:11: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Using H4/L4 values for current day 2025-05-20: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Drawing H4/L4 lines for 2025-05-20: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 67, Time: 20. 5. 2025 2:00:00, Position: Long
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 68, Time: 20. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 69, Time: 20. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 70, Time: 20. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 71, Time: 20. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 72, Time: 20. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 73, Time: 20. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 74, Time: 20. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 75, Time: 20. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 76, Time: 20. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 77, Time: 20. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 78, Time: 20. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:35:11: Processing bar: 79, Time: 20. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:35:11: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:35:11: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 1, Time: 14. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:36:01: Calculating Camarilla levels for date: 2025-05-14, CurrentBar: 1
20. 5. 2025 19:36:01: Debugging all available bars for date calculation:
20. 5. 2025 19:36:01: Bar 0: Time=14. 5. 2025 20:00:00, Date=14. 5. 2025 0:00:00, Open=5910, High=5910,25, Low=5890, Close=5908,25
20. 5. 2025 19:36:01: Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:36:01: Using default H4/L4 values for 2025-05-14 - could not find previous day's data
20. 5. 2025 19:36:01: IMPORTANT: Also caching default H4/L4 values for previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:36:01: Processing bar: 2, Time: 14. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:36:01: Processing bar: 3, Time: 14. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:36:01: Processing bar: 4, Time: 14. 5. 2025 23:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:36:01: Processing bar: 5, Time: 15. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 19:36:01: Calculating Camarilla levels for date: 2025-05-15, CurrentBar: 5
20. 5. 2025 19:36:01: Debugging all available bars for date calculation:
20. 5. 2025 19:36:01: Bar 0: Time=15. 5. 2025 1:00:00, Date=15. 5. 2025 0:00:00, Open=5904,25, High=5904,25, Low=5900, Close=5901,5
20. 5. 2025 19:36:01: Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Found 4 bars for previous day. Last bar index: 1, Close: 5906
20. 5. 2025 19:36:01: FINAL DATA FOR PREVIOUS DAY: High=5917,5, Low=5890, Close=5906
20. 5. 2025 19:36:01: Using standard Camarilla formula without corrections
20. 5. 2025 19:36:01: Calculation: Range=27,5, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:36:01: H4 = 5906 + (27,5 * 0,55) = 5921,12
20. 5. 2025 19:36:01: L4 = 5906 - (27,5 * 0,55) = 5890,88
20. 5. 2025 19:36:01: FINAL H4/L4 VALUES: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Calculated H4/L4 for 2025-05-15 based on previous day: H4=5921,12, L4=5890,88, PrevHigh=5917,5, PrevLow=5890, PrevClose=5906
20. 5. 2025 19:36:01: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Using H4/L4 values for current day 2025-05-15: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Drawing H4/L4 lines for 2025-05-15: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 6, Time: 15. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 7, Time: 15. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 8, Time: 15. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 9, Time: 15. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 10, Time: 15. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 11, Time: 15. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 12, Time: 15. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 13, Time: 15. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 14, Time: 15. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 15, Time: 15. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 16, Time: 15. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 17, Time: 15. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 18, Time: 15. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 19, Time: 15. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 20, Time: 15. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 21, Time: 15. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 22, Time: 15. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 23, Time: 15. 5. 2025 20:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 24, Time: 15. 5. 2025 21:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 25, Time: 15. 5. 2025 22:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 26, Time: 15. 5. 2025 23:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:36:01: Processing bar: 27, Time: 16. 5. 2025 1:00:00, Position: Long
20. 5. 2025 19:36:01: Calculating Camarilla levels for date: 2025-05-16, CurrentBar: 27
20. 5. 2025 19:36:01: Debugging all available bars for date calculation:
20. 5. 2025 19:36:01: Bar 0: Time=16. 5. 2025 1:00:00, Date=16. 5. 2025 0:00:00, Open=5935,5, High=5936, Low=5930,75, Close=5931
20. 5. 2025 19:36:01: Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Found 22 bars for previous day. Last bar index: 1, Close: 5934
20. 5. 2025 19:36:01: FINAL DATA FOR PREVIOUS DAY: High=5944,5, Low=5867, Close=5934
20. 5. 2025 19:36:01: Using standard Camarilla formula without corrections
20. 5. 2025 19:36:01: Calculation: Range=77,5, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:36:01: H4 = 5934 + (77,5 * 0,55) = 5976,62
20. 5. 2025 19:36:01: L4 = 5934 - (77,5 * 0,55) = 5891,38
20. 5. 2025 19:36:01: FINAL H4/L4 VALUES: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Calculated H4/L4 for 2025-05-16 based on previous day: H4=5976,62, L4=5891,38, PrevHigh=5944,5, PrevLow=5867, PrevClose=5934
20. 5. 2025 19:36:01: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Using H4/L4 values for current day 2025-05-16: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Drawing H4/L4 lines for 2025-05-16: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 28, Time: 16. 5. 2025 2:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 29, Time: 16. 5. 2025 3:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 30, Time: 16. 5. 2025 4:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 31, Time: 16. 5. 2025 5:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 32, Time: 16. 5. 2025 6:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 33, Time: 16. 5. 2025 7:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 34, Time: 16. 5. 2025 8:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 35, Time: 16. 5. 2025 9:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 36, Time: 16. 5. 2025 10:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 37, Time: 16. 5. 2025 11:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 38, Time: 16. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 39, Time: 16. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 40, Time: 16. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 41, Time: 16. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 42, Time: 16. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 43, Time: 16. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:36:01: Processing bar: 44, Time: 19. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 19:36:01: Calculating Camarilla levels for date: 2025-05-19, CurrentBar: 44
20. 5. 2025 19:36:01: Debugging all available bars for date calculation:
20. 5. 2025 19:36:01: Bar 0: Time=19. 5. 2025 1:00:00, Date=19. 5. 2025 0:00:00, Open=5930,5, High=5940,5, Low=5917,75, Close=5939
20. 5. 2025 19:36:01: Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Found 17 bars for previous day. Last bar index: 1, Close: 5936,75
20. 5. 2025 19:36:01: FINAL DATA FOR PREVIOUS DAY: High=5958,25, Low=5923, Close=5936,75
20. 5. 2025 19:36:01: Using standard Camarilla formula without corrections
20. 5. 2025 19:36:01: Calculation: Range=35,25, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:36:01: H4 = 5936,75 + (35,25 * 0,55) = 5956,14
20. 5. 2025 19:36:01: L4 = 5936,75 - (35,25 * 0,55) = 5917,36
20. 5. 2025 19:36:01: FINAL H4/L4 VALUES: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Calculated H4/L4 for 2025-05-19 based on previous day: H4=5956,14, L4=5917,36, PrevHigh=5958,25, PrevLow=5923, PrevClose=5936,75
20. 5. 2025 19:36:01: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Using H4/L4 values for current day 2025-05-19: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Drawing H4/L4 lines for 2025-05-19: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 45, Time: 19. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 46, Time: 19. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 47, Time: 19. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 48, Time: 19. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 49, Time: 19. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 50, Time: 19. 5. 2025 7:00:00, Position: Short
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 51, Time: 19. 5. 2025 8:00:00, Position: Short
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 52, Time: 19. 5. 2025 9:00:00, Position: Short
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 53, Time: 19. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 54, Time: 19. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 55, Time: 19. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 56, Time: 19. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 57, Time: 19. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 58, Time: 19. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 59, Time: 19. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 60, Time: 19. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 61, Time: 19. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 62, Time: 19. 5. 2025 20:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 63, Time: 19. 5. 2025 21:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 64, Time: 19. 5. 2025 22:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 65, Time: 19. 5. 2025 23:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:36:01: Processing bar: 66, Time: 20. 5. 2025 1:00:00, Position: Long
20. 5. 2025 19:36:01: Calculating Camarilla levels for date: 2025-05-20, CurrentBar: 66
20. 5. 2025 19:36:01: Debugging all available bars for date calculation:
20. 5. 2025 19:36:01: Bar 0: Time=20. 5. 2025 1:00:00, Date=20. 5. 2025 0:00:00, Open=5965, High=5968, Low=5963, Close=5967,25
20. 5. 2025 19:36:01: Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Found 22 bars for previous day. Last bar index: 1, Close: 5980,75
20. 5. 2025 19:36:01: FINAL DATA FOR PREVIOUS DAY: High=5987,5, Low=5892,75, Close=5980,75
20. 5. 2025 19:36:01: Using standard Camarilla formula without corrections
20. 5. 2025 19:36:01: Calculation: Range=94,75, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:36:01: H4 = 5980,75 + (94,75 * 0,55) = 6032,86
20. 5. 2025 19:36:01: L4 = 5980,75 - (94,75 * 0,55) = 5928,64
20. 5. 2025 19:36:01: FINAL H4/L4 VALUES: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Calculated H4/L4 for 2025-05-20 based on previous day: H4=6032,86, L4=5928,64, PrevHigh=5987,5, PrevLow=5892,75, PrevClose=5980,75
20. 5. 2025 19:36:01: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Using H4/L4 values for current day 2025-05-20: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Drawing H4/L4 lines for 2025-05-20: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 67, Time: 20. 5. 2025 2:00:00, Position: Long
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 68, Time: 20. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 69, Time: 20. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 70, Time: 20. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 71, Time: 20. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 72, Time: 20. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 73, Time: 20. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 74, Time: 20. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 75, Time: 20. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 76, Time: 20. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 77, Time: 20. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 78, Time: 20. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:36:01: Processing bar: 79, Time: 20. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:36:01: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:36:01: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 1, Time: 14. 5. 2025 20:00:00, Position: Flat
20. 5. 2025 19:48:52: Calculating Camarilla levels for date: 2025-05-14, CurrentBar: 1
20. 5. 2025 19:48:52: Debugging all available bars for date calculation:
20. 5. 2025 19:48:52: Bar 0: Time=14. 5. 2025 20:00:00, Date=14. 5. 2025 0:00:00, Open=5910, High=5910,25, Low=5890, Close=5908,25
20. 5. 2025 19:48:52: CME Trading Day - Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:48:52: Using default H4/L4 values for 2025-05-14 - could not find previous day's data
20. 5. 2025 19:48:52: IMPORTANT: Also caching default H4/L4 values for previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:48:52: Processing bar: 2, Time: 14. 5. 2025 21:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:48:52: Processing bar: 3, Time: 14. 5. 2025 22:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:48:52: Processing bar: 4, Time: 14. 5. 2025 23:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-14 (Wednesday), Previous trading day: 2025-05-13 (Tuesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-13: H4=5900, L4=5800
20. 5. 2025 19:48:52: Processing bar: 5, Time: 15. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 19:48:52: Calculating Camarilla levels for date: 2025-05-15, CurrentBar: 5
20. 5. 2025 19:48:52: Debugging all available bars for date calculation:
20. 5. 2025 19:48:52: Bar 0: Time=15. 5. 2025 1:00:00, Date=15. 5. 2025 0:00:00, Open=5904,25, High=5904,25, Low=5900, Close=5901,5
20. 5. 2025 19:48:52: CME Trading Day - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Found 4 bars for previous day. Last bar index: 1, Close: 5906
20. 5. 2025 19:48:52: FINAL DATA FOR PREVIOUS DAY: High=5917,5, Low=5890, Close=5906
20. 5. 2025 19:48:52: Using standard Camarilla formula without corrections
20. 5. 2025 19:48:52: Calculation: Range=27,5, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:48:52: H4 = 5906 + (27,5 * 0,55) = 5921,12
20. 5. 2025 19:48:52: L4 = 5906 - (27,5 * 0,55) = 5890,88
20. 5. 2025 19:48:52: FINAL H4/L4 VALUES: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Calculated H4/L4 for 2025-05-15 based on previous day: H4=5921,12, L4=5890,88, PrevHigh=5917,5, PrevLow=5890, PrevClose=5906
20. 5. 2025 19:48:52: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Using H4/L4 values for current day 2025-05-15: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Drawing H4/L4 lines for 2025-05-15: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 6, Time: 15. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 7, Time: 15. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 8, Time: 15. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 9, Time: 15. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 10, Time: 15. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 11, Time: 15. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 12, Time: 15. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 13, Time: 15. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 14, Time: 15. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 15, Time: 15. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 16, Time: 15. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 17, Time: 15. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 18, Time: 15. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 19, Time: 15. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 20, Time: 15. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 21, Time: 15. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 22, Time: 15. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 23, Time: 15. 5. 2025 20:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 24, Time: 15. 5. 2025 21:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 25, Time: 15. 5. 2025 22:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 26, Time: 15. 5. 2025 23:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-15 (Thursday), Previous trading day: 2025-05-14 (Wednesday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-14: H4=5921,12, L4=5890,88
20. 5. 2025 19:48:52: Processing bar: 27, Time: 16. 5. 2025 1:00:00, Position: Long
20. 5. 2025 19:48:52: Calculating Camarilla levels for date: 2025-05-16, CurrentBar: 27
20. 5. 2025 19:48:52: Debugging all available bars for date calculation:
20. 5. 2025 19:48:52: Bar 0: Time=16. 5. 2025 1:00:00, Date=16. 5. 2025 0:00:00, Open=5935,5, High=5936, Low=5930,75, Close=5931
20. 5. 2025 19:48:52: CME Trading Day - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Found 22 bars for previous day. Last bar index: 1, Close: 5934
20. 5. 2025 19:48:52: FINAL DATA FOR PREVIOUS DAY: High=5944,5, Low=5867, Close=5934
20. 5. 2025 19:48:52: Using standard Camarilla formula without corrections
20. 5. 2025 19:48:52: Calculation: Range=77,5, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:48:52: H4 = 5934 + (77,5 * 0,55) = 5976,62
20. 5. 2025 19:48:52: L4 = 5934 - (77,5 * 0,55) = 5891,38
20. 5. 2025 19:48:52: FINAL H4/L4 VALUES: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Calculated H4/L4 for 2025-05-16 based on previous day: H4=5976,62, L4=5891,38, PrevHigh=5944,5, PrevLow=5867, PrevClose=5934
20. 5. 2025 19:48:52: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Using H4/L4 values for current day 2025-05-16: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Drawing H4/L4 lines for 2025-05-16: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 28, Time: 16. 5. 2025 2:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 29, Time: 16. 5. 2025 3:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 30, Time: 16. 5. 2025 4:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 31, Time: 16. 5. 2025 5:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 32, Time: 16. 5. 2025 6:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 33, Time: 16. 5. 2025 7:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 34, Time: 16. 5. 2025 8:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 35, Time: 16. 5. 2025 9:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 36, Time: 16. 5. 2025 10:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 37, Time: 16. 5. 2025 11:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 38, Time: 16. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 39, Time: 16. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 40, Time: 16. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 41, Time: 16. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 42, Time: 16. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 43, Time: 16. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-16 (Friday), Previous trading day: 2025-05-15 (Thursday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-15: H4=5976,62, L4=5891,38
20. 5. 2025 19:48:52: Processing bar: 44, Time: 19. 5. 2025 1:00:00, Position: Flat
20. 5. 2025 19:48:52: Calculating Camarilla levels for date: 2025-05-19, CurrentBar: 44
20. 5. 2025 19:48:52: Debugging all available bars for date calculation:
20. 5. 2025 19:48:52: Bar 0: Time=19. 5. 2025 1:00:00, Date=19. 5. 2025 0:00:00, Open=5930,5, High=5940,5, Low=5917,75, Close=5939
20. 5. 2025 19:48:52: CME Trading Day - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Found 17 bars for previous day. Last bar index: 1, Close: 5936,75
20. 5. 2025 19:48:52: FINAL DATA FOR PREVIOUS DAY: High=5958,25, Low=5923, Close=5936,75
20. 5. 2025 19:48:52: Using standard Camarilla formula without corrections
20. 5. 2025 19:48:52: Calculation: Range=35,25, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:48:52: H4 = 5936,75 + (35,25 * 0,55) = 5956,14
20. 5. 2025 19:48:52: L4 = 5936,75 - (35,25 * 0,55) = 5917,36
20. 5. 2025 19:48:52: FINAL H4/L4 VALUES: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Calculated H4/L4 for 2025-05-19 based on previous day: H4=5956,14, L4=5917,36, PrevHigh=5958,25, PrevLow=5923, PrevClose=5936,75
20. 5. 2025 19:48:52: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Using H4/L4 values for current day 2025-05-19: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Drawing H4/L4 lines for 2025-05-19: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 45, Time: 19. 5. 2025 2:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 46, Time: 19. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 47, Time: 19. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 48, Time: 19. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 49, Time: 19. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 50, Time: 19. 5. 2025 7:00:00, Position: Short
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 51, Time: 19. 5. 2025 8:00:00, Position: Short
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 52, Time: 19. 5. 2025 9:00:00, Position: Short
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 53, Time: 19. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 54, Time: 19. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 55, Time: 19. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 56, Time: 19. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 57, Time: 19. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 58, Time: 19. 5. 2025 15:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 59, Time: 19. 5. 2025 16:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 60, Time: 19. 5. 2025 17:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 61, Time: 19. 5. 2025 19:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 62, Time: 19. 5. 2025 20:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 63, Time: 19. 5. 2025 21:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 64, Time: 19. 5. 2025 22:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 65, Time: 19. 5. 2025 23:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-16: H4=5956,14, L4=5917,36
20. 5. 2025 19:48:52: Processing bar: 66, Time: 20. 5. 2025 1:00:00, Position: Long
20. 5. 2025 19:48:52: Calculating Camarilla levels for date: 2025-05-20, CurrentBar: 66
20. 5. 2025 19:48:52: Debugging all available bars for date calculation:
20. 5. 2025 19:48:52: Bar 0: Time=20. 5. 2025 1:00:00, Date=20. 5. 2025 0:00:00, Open=5965, High=5968, Low=5963, Close=5967,25
20. 5. 2025 19:48:52: CME Trading Day - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Found 22 bars for previous day. Last bar index: 1, Close: 5980,75
20. 5. 2025 19:48:52: FINAL DATA FOR PREVIOUS DAY: High=5987,5, Low=5892,75, Close=5980,75
20. 5. 2025 19:48:52: Using standard Camarilla formula without corrections
20. 5. 2025 19:48:52: Calculation: Range=94,75, H4 Multiplier=0,55, L4 Multiplier=0,55
20. 5. 2025 19:48:52: H4 = 5980,75 + (94,75 * 0,55) = 6032,86
20. 5. 2025 19:48:52: L4 = 5980,75 - (94,75 * 0,55) = 5928,64
20. 5. 2025 19:48:52: FINAL H4/L4 VALUES: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Calculated H4/L4 for 2025-05-20 based on previous day: H4=6032,86, L4=5928,64, PrevHigh=5987,5, PrevLow=5892,75, PrevClose=5980,75
20. 5. 2025 19:48:52: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Using H4/L4 values for current day 2025-05-20: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Drawing H4/L4 lines for 2025-05-20: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 67, Time: 20. 5. 2025 2:00:00, Position: Long
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 68, Time: 20. 5. 2025 3:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 69, Time: 20. 5. 2025 4:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 70, Time: 20. 5. 2025 5:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 71, Time: 20. 5. 2025 6:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 72, Time: 20. 5. 2025 7:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 73, Time: 20. 5. 2025 8:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 74, Time: 20. 5. 2025 9:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 75, Time: 20. 5. 2025 10:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 76, Time: 20. 5. 2025 11:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 77, Time: 20. 5. 2025 12:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 78, Time: 20. 5. 2025 13:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
20. 5. 2025 19:48:52: Processing bar: 79, Time: 20. 5. 2025 14:00:00, Position: Flat
20. 5. 2025 19:48:52: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
20. 5. 2025 19:48:52: Using entry H4/L4 values from previous day 2025-05-19: H4=6032,86, L4=5928,64
25. 5. 2025 10:16:48: Processing bar: 1, Time: 19. 5. 2025 20:00:00, Position: Flat
25. 5. 2025 10:16:48: Calculating Camarilla levels for date: 2025-05-19, CurrentBar: 1
25. 5. 2025 10:16:48: Debugging all available bars for date calculation:
25. 5. 2025 10:16:48: Bar 0: Time=19. 5. 2025 20:00:00, Date=19. 5. 2025 0:00:00, Open=5977,25, High=5987,5, Low=5975, Close=5975,5
25. 5. 2025 10:16:48: CME Trading Day - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
25. 5. 2025 10:16:48: Using default H4/L4 values for 2025-05-19 - could not find previous day's data
25. 5. 2025 10:16:48: IMPORTANT: Also caching default H4/L4 values for previous day 2025-05-16: H4=5900, L4=5800
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-16: H4=5900, L4=5800
25. 5. 2025 10:16:48: Processing bar: 2, Time: 19. 5. 2025 21:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-16: H4=5900, L4=5800
25. 5. 2025 10:16:48: Processing bar: 3, Time: 19. 5. 2025 22:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-16: H4=5900, L4=5800
25. 5. 2025 10:16:48: Processing bar: 4, Time: 19. 5. 2025 23:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-16: H4=5900, L4=5800
25. 5. 2025 10:16:48: Processing bar: 5, Time: 20. 5. 2025 1:00:00, Position: Flat
25. 5. 2025 10:16:48: Calculating Camarilla levels for date: 2025-05-20, CurrentBar: 5
25. 5. 2025 10:16:48: Debugging all available bars for date calculation:
25. 5. 2025 10:16:48: Bar 0: Time=20. 5. 2025 1:00:00, Date=20. 5. 2025 0:00:00, Open=5965, High=5968, Low=5963, Close=5967,25
25. 5. 2025 10:16:48: CME Trading Day - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Found 4 bars for previous day. Last bar index: 1, Close: 5980,75
25. 5. 2025 10:16:48: FINAL DATA FOR PREVIOUS DAY: High=5987,5, Low=5962,75, Close=5980,75
25. 5. 2025 10:16:48: Using standard Camarilla formula without corrections
25. 5. 2025 10:16:48: Calculation: Range=24,75, H4 Multiplier=0,55, L4 Multiplier=0,55
25. 5. 2025 10:16:48: H4 = 5980,75 + (24,75 * 0,55) = 5994,36
25. 5. 2025 10:16:48: L4 = 5980,75 - (24,75 * 0,55) = 5967,14
25. 5. 2025 10:16:48: FINAL H4/L4 VALUES: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Calculated H4/L4 for 2025-05-20 based on previous day: H4=5994,36, L4=5967,14, PrevHigh=5987,5, PrevLow=5962,75, PrevClose=5980,75
25. 5. 2025 10:16:48: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Using H4/L4 values for current day 2025-05-20: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Drawing H4/L4 lines for 2025-05-20: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 6, Time: 20. 5. 2025 2:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 7, Time: 20. 5. 2025 3:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 8, Time: 20. 5. 2025 4:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 9, Time: 20. 5. 2025 5:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 10, Time: 20. 5. 2025 6:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 11, Time: 20. 5. 2025 7:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 12, Time: 20. 5. 2025 8:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 13, Time: 20. 5. 2025 9:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 14, Time: 20. 5. 2025 10:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 15, Time: 20. 5. 2025 11:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 16, Time: 20. 5. 2025 12:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 17, Time: 20. 5. 2025 13:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 18, Time: 20. 5. 2025 14:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 19, Time: 20. 5. 2025 15:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 20, Time: 20. 5. 2025 16:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 21, Time: 20. 5. 2025 17:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 22, Time: 20. 5. 2025 19:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 23, Time: 20. 5. 2025 20:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 24, Time: 20. 5. 2025 21:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 25, Time: 20. 5. 2025 22:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 26, Time: 20. 5. 2025 23:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 10:16:48: Processing bar: 27, Time: 21. 5. 2025 0:00:00, Position: Flat
25. 5. 2025 10:16:48: Calculating Camarilla levels for date: 2025-05-21, CurrentBar: 27
25. 5. 2025 10:16:48: Debugging all available bars for date calculation:
25. 5. 2025 10:16:48: Bar 0: Time=21. 5. 2025 0:00:00, Date=21. 5. 2025 0:00:00, Open=5942,5, High=5946, Low=5939,5, Close=5942,25
25. 5. 2025 10:16:48: CME Trading Day - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Found 22 bars for previous day. Last bar index: 1, Close: 5942,75
25. 5. 2025 10:16:48: FINAL DATA FOR PREVIOUS DAY: High=5974,75, Low=5926,5, Close=5942,75
25. 5. 2025 10:16:48: Using standard Camarilla formula without corrections
25. 5. 2025 10:16:48: Calculation: Range=48,25, H4 Multiplier=0,55, L4 Multiplier=0,55
25. 5. 2025 10:16:48: H4 = 5942,75 + (48,25 * 0,55) = 5969,29
25. 5. 2025 10:16:48: L4 = 5942,75 - (48,25 * 0,55) = 5916,21
25. 5. 2025 10:16:48: FINAL H4/L4 VALUES: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Calculated H4/L4 for 2025-05-21 based on previous day: H4=5969,29, L4=5916,21, PrevHigh=5974,75, PrevLow=5926,5, PrevClose=5942,75
25. 5. 2025 10:16:48: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Using H4/L4 values for current day 2025-05-21: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Drawing H4/L4 lines for 2025-05-21: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 28, Time: 21. 5. 2025 1:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 29, Time: 21. 5. 2025 2:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 30, Time: 21. 5. 2025 3:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 31, Time: 21. 5. 2025 4:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 32, Time: 21. 5. 2025 5:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 33, Time: 21. 5. 2025 6:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 34, Time: 21. 5. 2025 7:00:00, Position: Short
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 35, Time: 21. 5. 2025 8:00:00, Position: Short
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 36, Time: 21. 5. 2025 9:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 37, Time: 21. 5. 2025 10:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 38, Time: 21. 5. 2025 11:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 39, Time: 21. 5. 2025 12:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 40, Time: 21. 5. 2025 13:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 41, Time: 21. 5. 2025 14:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 42, Time: 21. 5. 2025 15:00:00, Position: Short
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 43, Time: 21. 5. 2025 16:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 44, Time: 21. 5. 2025 17:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 45, Time: 21. 5. 2025 19:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 46, Time: 21. 5. 2025 20:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 47, Time: 21. 5. 2025 21:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 48, Time: 21. 5. 2025 22:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 49, Time: 21. 5. 2025 23:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 10:16:48: Processing bar: 50, Time: 22. 5. 2025 0:00:00, Position: Flat
25. 5. 2025 10:16:48: Calculating Camarilla levels for date: 2025-05-22, CurrentBar: 50
25. 5. 2025 10:16:48: Debugging all available bars for date calculation:
25. 5. 2025 10:16:48: Bar 0: Time=22. 5. 2025 0:00:00, Date=22. 5. 2025 0:00:00, Open=5863, High=5868,5, Low=5863, Close=5865
25. 5. 2025 10:16:48: CME Trading Day - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Found 23 bars for previous day. Last bar index: 1, Close: 5863
25. 5. 2025 10:16:48: FINAL DATA FOR PREVIOUS DAY: High=5956,25, Low=5847,75, Close=5863
25. 5. 2025 10:16:48: Using standard Camarilla formula without corrections
25. 5. 2025 10:16:48: Calculation: Range=108,5, H4 Multiplier=0,55, L4 Multiplier=0,55
25. 5. 2025 10:16:48: H4 = 5863 + (108,5 * 0,55) = 5922,68
25. 5. 2025 10:16:48: L4 = 5863 - (108,5 * 0,55) = 5803,32
25. 5. 2025 10:16:48: FINAL H4/L4 VALUES: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Calculated H4/L4 for 2025-05-22 based on previous day: H4=5922,68, L4=5803,32, PrevHigh=5956,25, PrevLow=5847,75, PrevClose=5863
25. 5. 2025 10:16:48: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Using H4/L4 values for current day 2025-05-22: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Drawing H4/L4 lines for 2025-05-22: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 51, Time: 22. 5. 2025 1:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 52, Time: 22. 5. 2025 2:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 53, Time: 22. 5. 2025 3:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 54, Time: 22. 5. 2025 4:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 55, Time: 22. 5. 2025 5:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 56, Time: 22. 5. 2025 6:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 57, Time: 22. 5. 2025 7:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 58, Time: 22. 5. 2025 8:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 59, Time: 22. 5. 2025 9:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 60, Time: 22. 5. 2025 10:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 61, Time: 22. 5. 2025 11:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 62, Time: 22. 5. 2025 12:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 63, Time: 22. 5. 2025 13:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 64, Time: 22. 5. 2025 14:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 65, Time: 22. 5. 2025 15:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 66, Time: 22. 5. 2025 16:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 67, Time: 22. 5. 2025 17:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 68, Time: 22. 5. 2025 19:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 69, Time: 22. 5. 2025 20:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 70, Time: 22. 5. 2025 21:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 71, Time: 22. 5. 2025 22:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 72, Time: 22. 5. 2025 23:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 10:16:48: Processing bar: 73, Time: 23. 5. 2025 0:00:00, Position: Flat
25. 5. 2025 10:16:48: Calculating Camarilla levels for date: 2025-05-23, CurrentBar: 73
25. 5. 2025 10:16:48: Debugging all available bars for date calculation:
25. 5. 2025 10:16:48: Bar 0: Time=23. 5. 2025 0:00:00, Date=23. 5. 2025 0:00:00, Open=5863,25, High=5867,5, Low=5860, Close=5862,25
25. 5. 2025 10:16:48: CME Trading Day - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Found 23 bars for previous day. Last bar index: 1, Close: 5863
25. 5. 2025 10:16:48: FINAL DATA FOR PREVIOUS DAY: High=5895, Low=5828, Close=5863
25. 5. 2025 10:16:48: Using standard Camarilla formula without corrections
25. 5. 2025 10:16:48: Calculation: Range=67, H4 Multiplier=0,55, L4 Multiplier=0,55
25. 5. 2025 10:16:48: H4 = 5863 + (67 * 0,55) = 5899,85
25. 5. 2025 10:16:48: L4 = 5863 - (67 * 0,55) = 5826,15
25. 5. 2025 10:16:48: FINAL H4/L4 VALUES: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Calculated H4/L4 for 2025-05-23 based on previous day: H4=5899,85, L4=5826,15, PrevHigh=5895, PrevLow=5828, PrevClose=5863
25. 5. 2025 10:16:48: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Using H4/L4 values for current day 2025-05-23: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Drawing H4/L4 lines for 2025-05-23: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 74, Time: 23. 5. 2025 1:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 75, Time: 23. 5. 2025 2:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 76, Time: 23. 5. 2025 3:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 77, Time: 23. 5. 2025 4:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 78, Time: 23. 5. 2025 5:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 79, Time: 23. 5. 2025 6:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 80, Time: 23. 5. 2025 7:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 81, Time: 23. 5. 2025 8:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 82, Time: 23. 5. 2025 9:00:00, Position: Short
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 83, Time: 23. 5. 2025 10:00:00, Position: Short
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 84, Time: 23. 5. 2025 11:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 85, Time: 23. 5. 2025 12:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 86, Time: 23. 5. 2025 13:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 87, Time: 23. 5. 2025 14:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 88, Time: 23. 5. 2025 15:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 89, Time: 23. 5. 2025 16:00:00, Position: Flat
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 10:16:48: Processing bar: 90, Time: 23. 5. 2025 17:00:00, Position: Short
25. 5. 2025 10:16:48: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 10:16:48: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:26: Processing bar: 1, Time: 19. 5. 2025 20:00:00, Position: Flat
25. 5. 2025 11:20:26: Calculating Camarilla levels for date: 2025-05-19, CurrentBar: 1
25. 5. 2025 11:20:26: Debugging all available bars for date calculation:
25. 5. 2025 11:20:26: Bar 0: Time=19. 5. 2025 20:00:00, Date=19. 5. 2025 0:00:00, Open=5977,25, High=5987,5, Low=5975, Close=5975,5
25. 5. 2025 11:20:26: CME Trading Day - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
25. 5. 2025 11:20:26: Using default H4/L4 values for 2025-05-19 - could not find previous day's data
25. 5. 2025 11:20:26: IMPORTANT: Also caching default H4/L4 values for previous day 2025-05-16: H4=5900, L4=5800
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-16: H4=5900, L4=5800
25. 5. 2025 11:20:26: Processing bar: 2, Time: 19. 5. 2025 21:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-16: H4=5900, L4=5800
25. 5. 2025 11:20:26: Processing bar: 3, Time: 19. 5. 2025 22:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-16: H4=5900, L4=5800
25. 5. 2025 11:20:26: Processing bar: 4, Time: 19. 5. 2025 23:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-19 (Monday), Previous trading day: 2025-05-16 (Friday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-16: H4=5900, L4=5800
25. 5. 2025 11:20:26: Processing bar: 5, Time: 20. 5. 2025 1:00:00, Position: Flat
25. 5. 2025 11:20:26: Calculating Camarilla levels for date: 2025-05-20, CurrentBar: 5
25. 5. 2025 11:20:26: Debugging all available bars for date calculation:
25. 5. 2025 11:20:26: Bar 0: Time=20. 5. 2025 1:00:00, Date=20. 5. 2025 0:00:00, Open=5965, High=5968, Low=5963, Close=5967,25
25. 5. 2025 11:20:26: CME Trading Day - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Found 4 bars for previous day. Last bar index: 1, Close: 5980,75
25. 5. 2025 11:20:26: FINAL DATA FOR PREVIOUS DAY: High=5987,5, Low=5962,75, Close=5980,75
25. 5. 2025 11:20:26: Using standard Camarilla formula without corrections
25. 5. 2025 11:20:26: Calculation: Range=24,75, H4 Multiplier=0,55, L4 Multiplier=0,55
25. 5. 2025 11:20:26: H4 = 5980,75 + (24,75 * 0,55) = 5994,36
25. 5. 2025 11:20:26: L4 = 5980,75 - (24,75 * 0,55) = 5967,14
25. 5. 2025 11:20:26: FINAL H4/L4 VALUES: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Calculated H4/L4 for 2025-05-20 based on previous day: H4=5994,36, L4=5967,14, PrevHigh=5987,5, PrevLow=5962,75, PrevClose=5980,75
25. 5. 2025 11:20:26: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Using H4/L4 values for current day 2025-05-20: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Drawing H4/L4 lines for 2025-05-20: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 6, Time: 20. 5. 2025 2:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 7, Time: 20. 5. 2025 3:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 8, Time: 20. 5. 2025 4:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 9, Time: 20. 5. 2025 5:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 10, Time: 20. 5. 2025 6:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 11, Time: 20. 5. 2025 7:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 12, Time: 20. 5. 2025 8:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 13, Time: 20. 5. 2025 9:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 14, Time: 20. 5. 2025 10:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 15, Time: 20. 5. 2025 11:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 16, Time: 20. 5. 2025 12:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 17, Time: 20. 5. 2025 13:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 18, Time: 20. 5. 2025 14:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 19, Time: 20. 5. 2025 15:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 20, Time: 20. 5. 2025 16:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 21, Time: 20. 5. 2025 17:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 22, Time: 20. 5. 2025 19:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 23, Time: 20. 5. 2025 20:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 24, Time: 20. 5. 2025 21:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 25, Time: 20. 5. 2025 22:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 26, Time: 20. 5. 2025 23:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-20 (Tuesday), Previous trading day: 2025-05-19 (Monday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-19: H4=5994,36, L4=5967,14
25. 5. 2025 11:20:26: Processing bar: 27, Time: 21. 5. 2025 0:00:00, Position: Flat
25. 5. 2025 11:20:26: Calculating Camarilla levels for date: 2025-05-21, CurrentBar: 27
25. 5. 2025 11:20:26: Debugging all available bars for date calculation:
25. 5. 2025 11:20:26: Bar 0: Time=21. 5. 2025 0:00:00, Date=21. 5. 2025 0:00:00, Open=5942,5, High=5946, Low=5939,5, Close=5942,25
25. 5. 2025 11:20:26: CME Trading Day - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:26: Found 22 bars for previous day. Last bar index: 1, Close: 5942,75
25. 5. 2025 11:20:26: FINAL DATA FOR PREVIOUS DAY: High=5974,75, Low=5926,5, Close=5942,75
25. 5. 2025 11:20:26: Using standard Camarilla formula without corrections
25. 5. 2025 11:20:26: Calculation: Range=48,25, H4 Multiplier=0,55, L4 Multiplier=0,55
25. 5. 2025 11:20:26: H4 = 5942,75 + (48,25 * 0,55) = 5969,29
25. 5. 2025 11:20:26: L4 = 5942,75 - (48,25 * 0,55) = 5916,21
25. 5. 2025 11:20:26: FINAL H4/L4 VALUES: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:26: Calculated H4/L4 for 2025-05-21 based on previous day: H4=5969,29, L4=5916,21, PrevHigh=5974,75, PrevLow=5926,5, PrevClose=5942,75
25. 5. 2025 11:20:26: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:26: Using H4/L4 values for current day 2025-05-21: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:26: Drawing H4/L4 lines for 2025-05-21: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:26: Processing bar: 28, Time: 21. 5. 2025 1:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:26: Processing bar: 29, Time: 21. 5. 2025 2:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:26: Processing bar: 30, Time: 21. 5. 2025 3:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:26: Processing bar: 31, Time: 21. 5. 2025 4:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:26: Processing bar: 32, Time: 21. 5. 2025 5:00:00, Position: Flat
25. 5. 2025 11:20:26: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:26: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:26: Processing bar: 33, Time: 21. 5. 2025 6:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 34, Time: 21. 5. 2025 7:00:00, Position: Short
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 35, Time: 21. 5. 2025 8:00:00, Position: Short
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 36, Time: 21. 5. 2025 9:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 37, Time: 21. 5. 2025 10:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 38, Time: 21. 5. 2025 11:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 39, Time: 21. 5. 2025 12:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 40, Time: 21. 5. 2025 13:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 41, Time: 21. 5. 2025 14:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 42, Time: 21. 5. 2025 15:00:00, Position: Short
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 43, Time: 21. 5. 2025 16:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 44, Time: 21. 5. 2025 17:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 45, Time: 21. 5. 2025 19:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 46, Time: 21. 5. 2025 20:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 47, Time: 21. 5. 2025 21:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 48, Time: 21. 5. 2025 22:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 49, Time: 21. 5. 2025 23:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-21 (Wednesday), Previous trading day: 2025-05-20 (Tuesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-20: H4=5969,29, L4=5916,21
25. 5. 2025 11:20:27: Processing bar: 50, Time: 22. 5. 2025 0:00:00, Position: Flat
25. 5. 2025 11:20:27: Calculating Camarilla levels for date: 2025-05-22, CurrentBar: 50
25. 5. 2025 11:20:27: Debugging all available bars for date calculation:
25. 5. 2025 11:20:27: Bar 0: Time=22. 5. 2025 0:00:00, Date=22. 5. 2025 0:00:00, Open=5863, High=5868,5, Low=5863, Close=5865
25. 5. 2025 11:20:27: CME Trading Day - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Found 23 bars for previous day. Last bar index: 1, Close: 5863
25. 5. 2025 11:20:27: FINAL DATA FOR PREVIOUS DAY: High=5956,25, Low=5847,75, Close=5863
25. 5. 2025 11:20:27: Using standard Camarilla formula without corrections
25. 5. 2025 11:20:27: Calculation: Range=108,5, H4 Multiplier=0,55, L4 Multiplier=0,55
25. 5. 2025 11:20:27: H4 = 5863 + (108,5 * 0,55) = 5922,68
25. 5. 2025 11:20:27: L4 = 5863 - (108,5 * 0,55) = 5803,32
25. 5. 2025 11:20:27: FINAL H4/L4 VALUES: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Calculated H4/L4 for 2025-05-22 based on previous day: H4=5922,68, L4=5803,32, PrevHigh=5956,25, PrevLow=5847,75, PrevClose=5863
25. 5. 2025 11:20:27: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Using H4/L4 values for current day 2025-05-22: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Drawing H4/L4 lines for 2025-05-22: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 51, Time: 22. 5. 2025 1:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 52, Time: 22. 5. 2025 2:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 53, Time: 22. 5. 2025 3:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 54, Time: 22. 5. 2025 4:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 55, Time: 22. 5. 2025 5:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 56, Time: 22. 5. 2025 6:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 57, Time: 22. 5. 2025 7:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 58, Time: 22. 5. 2025 8:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 59, Time: 22. 5. 2025 9:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 60, Time: 22. 5. 2025 10:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 61, Time: 22. 5. 2025 11:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 62, Time: 22. 5. 2025 12:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 63, Time: 22. 5. 2025 13:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 64, Time: 22. 5. 2025 14:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 65, Time: 22. 5. 2025 15:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 66, Time: 22. 5. 2025 16:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 67, Time: 22. 5. 2025 17:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 68, Time: 22. 5. 2025 19:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 69, Time: 22. 5. 2025 20:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 70, Time: 22. 5. 2025 21:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 71, Time: 22. 5. 2025 22:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 72, Time: 22. 5. 2025 23:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-22 (Thursday), Previous trading day: 2025-05-21 (Wednesday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-21: H4=5922,68, L4=5803,32
25. 5. 2025 11:20:27: Processing bar: 73, Time: 23. 5. 2025 0:00:00, Position: Flat
25. 5. 2025 11:20:27: Calculating Camarilla levels for date: 2025-05-23, CurrentBar: 73
25. 5. 2025 11:20:27: Debugging all available bars for date calculation:
25. 5. 2025 11:20:27: Bar 0: Time=23. 5. 2025 0:00:00, Date=23. 5. 2025 0:00:00, Open=5863,25, High=5867,5, Low=5860, Close=5862,25
25. 5. 2025 11:20:27: CME Trading Day - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Found 23 bars for previous day. Last bar index: 1, Close: 5863
25. 5. 2025 11:20:27: FINAL DATA FOR PREVIOUS DAY: High=5895, Low=5828, Close=5863
25. 5. 2025 11:20:27: Using standard Camarilla formula without corrections
25. 5. 2025 11:20:27: Calculation: Range=67, H4 Multiplier=0,55, L4 Multiplier=0,55
25. 5. 2025 11:20:27: H4 = 5863 + (67 * 0,55) = 5899,85
25. 5. 2025 11:20:27: L4 = 5863 - (67 * 0,55) = 5826,15
25. 5. 2025 11:20:27: FINAL H4/L4 VALUES: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Calculated H4/L4 for 2025-05-23 based on previous day: H4=5899,85, L4=5826,15, PrevHigh=5895, PrevLow=5828, PrevClose=5863
25. 5. 2025 11:20:27: IMPORTANT: Also caching H4/L4 values for previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Using H4/L4 values for current day 2025-05-23: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Drawing H4/L4 lines for 2025-05-23: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 74, Time: 23. 5. 2025 1:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 75, Time: 23. 5. 2025 2:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 76, Time: 23. 5. 2025 3:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 77, Time: 23. 5. 2025 4:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 78, Time: 23. 5. 2025 5:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 79, Time: 23. 5. 2025 6:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 80, Time: 23. 5. 2025 7:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 81, Time: 23. 5. 2025 8:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 82, Time: 23. 5. 2025 9:00:00, Position: Short
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 83, Time: 23. 5. 2025 10:00:00, Position: Short
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 84, Time: 23. 5. 2025 11:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 85, Time: 23. 5. 2025 12:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 86, Time: 23. 5. 2025 13:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 87, Time: 23. 5. 2025 14:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 88, Time: 23. 5. 2025 15:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 89, Time: 23. 5. 2025 16:00:00, Position: Flat
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
25. 5. 2025 11:20:27: Processing bar: 90, Time: 23. 5. 2025 17:00:00, Position: Short
25. 5. 2025 11:20:27: Entry conditions - Current date: 2025-05-23 (Friday), Previous trading day: 2025-05-22 (Thursday)
25. 5. 2025 11:20:27: Using entry H4/L4 values from previous day 2025-05-22: H4=5899,85, L4=5826,15
