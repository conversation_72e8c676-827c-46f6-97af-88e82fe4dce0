<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CamarillaLiveStrategy Documentation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        code {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            padding: 2px 4px;
            color: #c7254e;
        }
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 10px;
            overflow: auto;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .note {
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
            padding: 10px;
            margin: 15px 0;
        }
        .warning {
            background-color: #ffffcc;
            border-left: 6px solid #ffeb3b;
            padding: 10px;
            margin: 15px 0;
        }
        .formula {
            font-style: italic;
            margin: 10px 0;
            padding-left: 20px;
        }
        .parameter-section {
            margin-bottom: 30px;
        }
        .visualization-item {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>CamarillaLiveStrategy Documentation</h1>
    
    <h2>Overview</h2>
    <p>
        CamarillaLiveStrategy is a trading strategy for NinjaTrader that uses Camarilla levels to generate trading signals.
        The strategy calculates H4 and L4 levels from the previous trading day and uses them to enter positions.
        It also implements trailing stops and fixed stop losses for risk management.
    </p>

    <h2>How It Works</h2>
    <p>
        The strategy uses Camarilla levels, which are calculated from the previous trading day's data.
        These levels serve as support and resistance for the current trading day. The strategy enters
        a long position when the price crosses above the H4 level, and a short position when the price
        crosses below the L4 level.
    </p>

    <h2>Parameters</h2>
    <div class="parameter-section">
        <table>
            <tr>
                <th>Parameter</th>
                <th>Description</th>
                <th>Default Value</th>
            </tr>
            <tr>
                <td>EmaPeriod</td>
                <td>Period for EMA indicator</td>
                <td>8</td>
            </tr>
            <tr>
                <td>LongTrailPoints</td>
                <td>Number of ticks price must move in your favor to activate trailing stop for long positions</td>
                <td>40</td>
            </tr>
            <tr>
                <td>LongTrailOffset</td>
                <td>Offset for trailing stop for long positions (in ticks)</td>
                <td>1</td>
            </tr>
            <tr>
                <td>LongStopLoss</td>
                <td>Stop loss for long positions (in ticks)</td>
                <td>70</td>
            </tr>
            <tr>
                <td>ShortTrailPoints</td>
                <td>Number of ticks price must move in your favor to activate trailing stop for short positions</td>
                <td>20</td>
            </tr>
            <tr>
                <td>ShortTrailOffset</td>
                <td>Offset for trailing stop for short positions (in ticks)</td>
                <td>1</td>
            </tr>
            <tr>
                <td>ShortStopLoss</td>
                <td>Stop loss for short positions (in ticks)</td>
                <td>40</td>
            </tr>
        </table>
    </div>

    <h2>Camarilla Level Calculation</h2>
    <p>
        The strategy calculates H4 and L4 levels using the standard Camarilla formula:
    </p>
    <div class="formula">
        <p>H4 = Close + (High - Low) * 1.1/2</p>
        <p>L4 = Close - (High - Low) * 1.1/2</p>
    </div>
    <p>
        Where Close, High, and Low are values from the previous trading day.
    </p>

    <h2>Entry Conditions</h2>
    <ul>
        <li><strong>Long Entry</strong>: Price crosses above H4 level (CrossAbove) and Close > H4</li>
        <li><strong>Short Entry</strong>: Price crosses below L4 level (CrossBelow) and Close < L4</li>
    </ul>
    <p>
        Entry conditions are evaluated only at bar close or at the first tick of a bar,
        to ensure behavior similar to TradingView.
    </p>

    <h2>Risk Management</h2>
    <p>
        The strategy uses two mechanisms for risk management:
    </p>
    
    <h3>1. Fixed Stop Loss</h3>
    <ul>
        <li><strong>Long positions</strong>: Entry price - (LongStopLoss * TickSize)</li>
        <li><strong>Short positions</strong>: Entry price + (ShortStopLoss * TickSize)</li>
    </ul>
    
    <h3>2. Trailing Stop</h3>
    <ul>
        <li>
            <strong>Long positions</strong>: Activates when price rises by LongTrailPoints ticks.
            Stop is set at highest price - (LongTrailOffset * TickSize).
        </li>
        <li>
            <strong>Short positions</strong>: Activates when price falls by ShortTrailPoints ticks.
            Stop is set at lowest price + (ShortTrailOffset * TickSize).
        </li>
    </ul>

    <h2>Visualization</h2>
    <p>
        The strategy displays the following elements on the chart:
    </p>
    <ul>
        <li class="visualization-item"><strong>Orange horizontal lines</strong>: H4 and L4 levels for the current day</li>
        <li class="visualization-item"><strong>Red dashed lines</strong>: Fixed stop loss</li>
        <li class="visualization-item"><strong>Blue dashed lines</strong>: Trailing stop</li>
        <li class="visualization-item"><strong>Green arrows</strong>: Long entries</li>
        <li class="visualization-item"><strong>Red arrows</strong>: Short entries</li>
    </ul>

    <h2>Special Features</h2>
    <h3>Caching Levels</h3>
    <p>
        The strategy caches calculated H4/L4 levels to avoid recalculating them on every tick.
    </p>
    
    <h3>Weekend Handling</h3>
    <p>
        The strategy correctly identifies the previous trading day, even when the current day is Monday
        (it takes data from Friday).
    </p>
    
    <h3>Logging</h3>
    <p>
        The strategy writes important information to a log file for debugging purposes.
    </p>

    <h2>Installation and Usage</h2>
    <ol>
        <li>Compile the strategy in NinjaTrader</li>
        <li>Add the strategy to a chart</li>
        <li>Set parameters as needed</li>
        <li>Run the strategy in backtesting or live trading mode</li>
    </ol>

    <h2>Notes</h2>
    <div class="note">
        <p>
            The strategy is designed to be used with Calculate = OnEachTick, to ensure proper functioning
            of trailing stops in real-time.
        </p>
    </div>
    <div class="note">
        <p>
            Entry conditions are evaluated only at bar close or at the first tick of a bar,
            to ensure behavior similar to TradingView.
        </p>
    </div>
    <div class="note">
        <p>
            The strategy uses an EMA indicator, but currently doesn't use it for entry conditions.
        </p>
    </div>
</body>
</html>
